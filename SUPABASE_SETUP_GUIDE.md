# 🚀 دليل إعداد Supabase للدردشة الحية

## 📋 نظرة عامة

هذا الدليل يوضح كيفية إعداد Supabase لنظام الدردشة الحية الجماعية المجاني 100%.

---

## 🎯 الخطوة 1: إنشاء حساب Supabase

### 1. زيارة الموقع
- اذهب إلى [supabase.com](https://supabase.com)
- انقر على "Start your project"

### 2. إنشاء حساب
- سجل الدخول باستخدام GitHub (الأسهل)
- أو أنشئ حساب جديد بالإيميل

### 3. إنشاء مشروع جديد
- انقر على "New Project"
- اختر Organization (أو أنشئ واحدة جديدة)
- أدخل تفاصيل المشروع:
  - **Name**: `hodifa-chat-system`
  - **Database Password**: كلمة مرور قوية (احفظها!)
  - **Region**: اختر أقرب منطقة (مثل Frankfurt)
- انقر على "Create new project"

---

## 🗄️ الخطوة 2: إعداد قاعدة البيانات

### 1. إنشاء جدول الرسائل

انتقل إلى **SQL Editor** وشغل هذا الكود:

```sql
-- إنشاء جدول الرسائل
CREATE TABLE chat_messages (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  username TEXT NOT NULL,
  message TEXT NOT NULL,
  user_color TEXT DEFAULT '#6B7280',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء فهرس للبحث السريع
CREATE INDEX idx_chat_messages_created_at ON chat_messages(created_at DESC);

-- تفعيل Row Level Security
ALTER TABLE chat_messages ENABLE ROW LEVEL SECURITY;

-- سياسة للقراءة (الجميع يمكنهم قراءة الرسائل)
CREATE POLICY "Anyone can read messages" ON chat_messages
  FOR SELECT USING (true);

-- سياسة للكتابة (الجميع يمكنهم إرسال رسائل)
CREATE POLICY "Anyone can insert messages" ON chat_messages
  FOR INSERT WITH CHECK (true);
```

### 2. إنشاء جدول المستخدمين (اختياري)

```sql
-- إنشاء جدول المستخدمين المتصلين
CREATE TABLE chat_users (
  username TEXT PRIMARY KEY,
  is_online BOOLEAN DEFAULT true,
  last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  user_color TEXT DEFAULT '#6B7280'
);

-- تفعيل Row Level Security
ALTER TABLE chat_users ENABLE ROW LEVEL SECURITY;

-- سياسة للقراءة
CREATE POLICY "Anyone can read users" ON chat_users
  FOR SELECT USING (true);

-- سياسة للكتابة
CREATE POLICY "Anyone can upsert users" ON chat_users
  FOR ALL USING (true);
```

### 3. تفعيل Realtime

انتقل إلى **Database > Replication** وفعل Realtime للجداول:
- ✅ `chat_messages`
- ✅ `chat_users`

---

## 🔑 الخطوة 3: الحصول على مفاتيح API

### 1. انتقل إلى Settings > API

### 2. انسخ المعلومات التالية:
- **Project URL**: `https://your-project-ref.supabase.co`
- **anon public key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`

---

## ⚙️ الخطوة 4: إعداد متغيرات البيئة

### 1. إنشاء ملف `.env`

```bash
# انسخ من .env.example
cp .env.example .env
```

### 2. تحديث القيم

```env
# Supabase Configuration
VITE_SUPABASE_URL=https://your-project-ref.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key-here
```

---

## 📦 الخطوة 5: تثبيت التبعيات

```bash
# تثبيت Supabase client
npm install @supabase/supabase-js

# أو إذا كان موجود في package.json
npm install
```

---

## 🧪 الخطوة 6: اختبار النظام

### 1. تشغيل المشروع محلياً

```bash
npm run dev
```

### 2. زيارة صفحة الدردشة

```
http://localhost:8080/group-chat
```

### 3. اختبار الميزات
- ✅ إدخال اسم المستخدم
- ✅ إرسال رسالة
- ✅ استقبال رسائل فورية
- ✅ عرض الرسائل السابقة

---

## 🔧 استكشاف الأخطاء

### مشكلة: "Invalid API key"
- تأكد من نسخ `anon key` وليس `service_role key`
- تحقق من عدم وجود مسافات إضافية

### مشكلة: "Table doesn't exist"
- تأكد من تشغيل SQL commands في SQL Editor
- تحقق من اسم الجدول `chat_messages`

### مشكلة: "Permission denied"
- تأكد من تفعيل Row Level Security policies
- تحقق من صحة السياسات

### مشكلة: "Realtime not working"
- تأكد من تفعيل Realtime للجداول
- تحقق من اتصال الإنترنت

---

## 📊 حدود الخطة المجانية

### ✅ ما تحصل عليه مجاناً:
- **قاعدة البيانات**: 500MB
- **نقل البيانات**: 2GB/شهر
- **اتصالات متزامنة**: 500
- **Realtime**: مفعل
- **API requests**: غير محدود
- **Row Level Security**: مفعل

### 📈 إذا احتجت أكثر:
- **Pro Plan**: $25/شهر
- **Team Plan**: $599/شهر

---

## 🎉 تهانينا!

الآن لديك نظام دردشة حية مجاني 100% يعمل مع:
- ✅ PostgreSQL قاعدة بيانات قوية
- ✅ Realtime subscriptions
- ✅ أمان متقدم مع RLS
- ✅ واجهة جميلة مع React
- ✅ TypeScript للأمان

---

## 🔗 روابط مفيدة

- [Supabase Documentation](https://supabase.com/docs)
- [Supabase Realtime](https://supabase.com/docs/guides/realtime)
- [Row Level Security](https://supabase.com/docs/guides/auth/row-level-security)
- [SQL Reference](https://supabase.com/docs/guides/database/overview)

---

**تم إنشاؤه بواسطة**: حذيفة عبدالمعز  
**التاريخ**: يناير 2025  
**الإصدار**: 1.0.0
