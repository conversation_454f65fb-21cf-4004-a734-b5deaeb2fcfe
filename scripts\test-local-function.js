#!/usr/bin/env node

/**
 * اختبار Firebase Function للأخبار التقنية العربية
 */

// اختبار Firebase Function عبر HTTP

async function testNetlifyFunction() {
  console.log('🧪 اختبار Netlify Function للأخبار التقنية العربية...\n');
  console.log('=' .repeat(50));

  try {
    const functionUrl = 'http://localhost:8080/.netlify/functions/news';

    console.log('🔄 إرسال طلب إلى Netlify Function...');
    console.log(`📡 URL: ${functionUrl}`);

    const response = await fetch(functionUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });

    if (response.ok) {
      const data = await response.json();

      if (data.success && data.articles && data.articles.length > 0) {
        console.log('✅ Netlify Function تعمل بنجاح!');
        console.log(`📊 عدد المقالات: ${data.articles.length}`);
        console.log(`📡 المصدر: ${data.source}`);
        console.log(`🕒 الوقت: ${data.timestamp}`);

        // عرض أول مقال كمثال
        const firstArticle = data.articles[0];
        console.log('\n📰 مثال على مقال:');
        console.log(`العنوان: ${firstArticle.title}`);
        console.log(`المصدر: ${firstArticle.source_id}`);
        console.log(`اللغة: ${firstArticle.language || 'غير محدد'}`);

        // عرض إحصائيات اللغات
        const arabicCount = data.articles.filter(a => a.language === 'ar').length;
        const englishCount = data.articles.filter(a => a.language === 'en').length;
        const otherCount = data.articles.filter(a => !a.language || (a.language !== 'ar' && a.language !== 'en')).length;

        console.log('\n📊 إحصائيات اللغات:');
        console.log(`🇸🇦 العربية: ${arabicCount} مقال`);
        console.log(`🇺🇸 الإنجليزية: ${englishCount} مقال`);
        if (otherCount > 0) {
          console.log(`🌐 أخرى: ${otherCount} مقال`);
        }

        // عرض الكلمات المفتاحية المستخدمة
        console.log('\n🔍 الكلمات المفتاحية المستخدمة:');
        console.log('- تقنية، تكنولوجيا، ذكاء اصطناعي');
        console.log('- برمجة، تطوير، حاسوب، إنترنت');

        return true;
      } else {
        console.log('⚠️ Netlify Function ترجع بيانات فارغة');
        console.log('Response:', data);
        return false;
      }
    } else {
      console.log(`❌ Netlify Function فشلت مع status: ${response.status}`);
      const errorText = await response.text();
      console.log('Response:', errorText);
      return false;
    }
    
  } catch (error) {
    console.error('❌ خطأ في تشغيل Function:', error.message);
    return false;
  }
}

// تشغيل الاختبار
testNetlifyFunction()
  .then(success => {
    console.log('\n' + '=' .repeat(50));
    if (success) {
      console.log('🎉 Netlify Function للأخبار التقنية العربية تعمل بنجاح!');
      console.log('✅ النظام جاهز للاستخدام في الإنتاج');
      console.log('📰 يمكن للمستخدمين الآن الاستمتاع بالأخبار التقنية العربية');
      console.log('💰 التكلفة: مجاني 100% مع Netlify Functions');
    } else {
      console.log('⚠️ هناك مشاكل تحتاج إلى إصلاح.');
      console.log('💡 تأكد من تشغيل الخادم المحلي أولاً باستخدام:');
      console.log('   npm run dev');
    }
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('❌ خطأ في تشغيل الاختبارات:', error);
    process.exit(1);
  });
