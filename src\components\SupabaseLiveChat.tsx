/**
 * مكون الدردشة الجماعية الحية باستخدام Supabase
 * مجاني 100% - بدون Firebase
 */

import React from 'react';
import { MessageCircle, Users, Settings, Wifi, WifiOff, RefreshCw } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

// مكونات الدردشة
import ChatMessageList from '@/components/chat/ChatMessageList';
import ChatInput from '@/components/chat/ChatInput';
import UsernameSetup from '@/components/chat/UsernameSetup';

// Hook الدردشة
import { useChat } from '@/hooks/useChat';

const SupabaseLiveChat: React.FC = () => {
  // استخدام hook الدردشة
  const {
    messages,
    isLoading,
    isConnected,
    error,
    username,
    userColor,
    isUsernameSet,
    sendMessage,
    setUsername,
    clearMessages,
    messageCount,
    onlineUsers,
  } = useChat({
    maxMessages: 100,
    autoScroll: true,
  });

  // إعادة تحميل الدردشة
  const handleRefresh = () => {
    window.location.reload();
  };

  // تغيير اسم المستخدم
  const handleChangeUsername = () => {
    const newUsername = prompt('أدخل اسم المستخدم الجديد:', username);
    if (newUsername && newUsername.trim()) {
      setUsername(newUsername.trim());
    }
  };

  // إذا لم يتم تعيين اسم المستخدم، عرض شاشة الإعداد
  if (!isUsernameSet) {
    return (
      <div className="max-w-4xl mx-auto">
        <UsernameSetup
          currentUsername={username}
          onUsernameSet={setUsername}
          error={error}
        />
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      <Card className="bg-gray-900/50 border-gray-700/50 shadow-2xl">
        {/* رأس الدردشة */}
        <CardHeader className="border-b border-gray-700/50">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-3">
              <div className="w-10 h-10 bg-amber-500/20 rounded-full flex items-center justify-center">
                <MessageCircle className="w-5 h-5 text-amber-400" />
              </div>
              <div>
                <h3 className="text-xl font-bold text-white">الدردشة الجماعية</h3>
                <p className="text-sm text-gray-400">مرحباً {username}!</p>
              </div>
            </CardTitle>

            <div className="flex items-center gap-4">
              {/* حالة الاتصال */}
              <div className="flex items-center gap-2">
                {isConnected ? (
                  <>
                    <Wifi className="w-4 h-4 text-green-400" />
                    <span className="text-sm text-green-400">متصل</span>
                  </>
                ) : (
                  <>
                    <WifiOff className="w-4 h-4 text-red-400" />
                    <span className="text-sm text-red-400">غير متصل</span>
                  </>
                )}
              </div>

              {/* عدد المستخدمين */}
              <div className="flex items-center gap-2">
                <Users className="w-4 h-4 text-blue-400" />
                <span className="text-sm text-blue-400">{onlineUsers} متصل</span>
              </div>

              {/* أزرار الإعدادات */}
              <div className="flex items-center gap-2">
                <Button
                  onClick={handleRefresh}
                  variant="outline"
                  size="sm"
                  className="border-gray-600 text-gray-300 hover:bg-gray-700/50"
                  title="إعادة تحميل"
                >
                  <RefreshCw className="w-4 h-4" />
                </Button>

                <Button
                  onClick={handleChangeUsername}
                  variant="outline"
                  size="sm"
                  className="border-gray-600 text-gray-300 hover:bg-gray-700/50"
                  title="تغيير اسم المستخدم"
                >
                  <Settings className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* معلومات إضافية */}
          <div className="flex items-center justify-between text-xs text-gray-500 mt-2">
            <div className="flex items-center gap-4">
              <span>{messageCount} رسالة</span>
              <span>آخر تحديث: الآن</span>
              <span>Supabase Realtime</span>
            </div>
            
            <div className="flex items-center gap-2">
              <div 
                className="w-3 h-3 rounded-full border-2 border-white"
                style={{ backgroundColor: userColor }}
              ></div>
              <span>لونك</span>
            </div>
          </div>
        </CardHeader>

        {/* محتوى الدردشة */}
        <CardContent className="p-0">
          <div className="flex flex-col h-[600px]">
            {/* قائمة الرسائل */}
            <div className="flex-1 p-4">
              <ChatMessageList
                messages={messages}
                currentUsername={username}
                isLoading={isLoading}
                isConnected={isConnected}
                error={error}
                onRetry={handleRefresh}
              />
            </div>

            {/* حقل الإدخال */}
            <div className="border-t border-gray-700/50 p-4">
              <ChatInput
                onSendMessage={sendMessage}
                disabled={!isConnected}
                placeholder={
                  isConnected 
                    ? "اكتب رسالتك هنا..." 
                    : "غير متصل - يرجى المحاولة لاحقاً"
                }
                maxLength={500}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* معلومات إضافية أسفل الدردشة */}
      <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="bg-gray-900/30 border-amber-500/20">
          <CardContent className="p-4 text-center">
            <MessageCircle className="w-6 h-6 text-amber-400 mx-auto mb-2" />
            <h4 className="font-semibold text-amber-400 mb-1">رسائل حية</h4>
            <p className="text-xs text-gray-400">تحديث فوري مع Supabase Realtime</p>
          </CardContent>
        </Card>

        <Card className="bg-gray-900/30 border-green-500/20">
          <CardContent className="p-4 text-center">
            <Users className="w-6 h-6 text-green-400 mx-auto mb-2" />
            <h4 className="font-semibold text-green-400 mb-1">متعدد المستخدمين</h4>
            <p className="text-xs text-gray-400">دردشة مع عدة أشخاص</p>
          </CardContent>
        </Card>

        <Card className="bg-gray-900/30 border-blue-500/20">
          <CardContent className="p-4 text-center">
            <Wifi className="w-6 h-6 text-blue-400 mx-auto mb-2" />
            <h4 className="font-semibold text-blue-400 mb-1">مجاني 100%</h4>
            <p className="text-xs text-gray-400">بدون تكاليف أو قيود</p>
          </CardContent>
        </Card>
      </div>

      {/* تحذير إعداد Supabase */}
      {(!import.meta.env.VITE_SUPABASE_URL || !import.meta.env.VITE_SUPABASE_ANON_KEY) && (
        <div className="mt-4 p-4 bg-red-900/20 border border-red-500/30 rounded-lg">
          <h4 className="text-red-400 font-semibold mb-2">⚠️ إعداد Supabase مطلوب</h4>
          <p className="text-red-300 text-sm mb-2">
            يرجى إعداد متغيرات البيئة التالية في ملف .env:
          </p>
          <ul className="text-red-300 text-xs space-y-1">
            <li>• VITE_SUPABASE_URL=https://your-project.supabase.co</li>
            <li>• VITE_SUPABASE_ANON_KEY=your-anon-key</li>
          </ul>
        </div>
      )}

      {/* معلومات للمطورين */}
      {process.env.NODE_ENV === 'development' && (
        <div className="mt-4 p-4 bg-blue-900/20 border border-blue-500/30 rounded-lg">
          <h4 className="text-blue-400 font-semibold mb-2">💡 معلومات للمطورين</h4>
          <div className="text-blue-300 text-sm space-y-1">
            <p>• قاعدة البيانات: PostgreSQL (Supabase)</p>
            <p>• الاتصال الحي: Supabase Realtime</p>
            <p>• التكلفة: مجاني 100% (500MB قاعدة بيانات + 2GB نقل)</p>
            <p>• الحد الأقصى: 500 اتصال متزامن</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default SupabaseLiveChat;
