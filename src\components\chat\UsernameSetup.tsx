/**
 * مكون إعداد اسم المستخدم للدردشة
 */

import React, { useState } from 'react';
import { User, Shuffle, Check, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { chatUtils } from '@/lib/supabase';

interface UsernameSetupProps {
  currentUsername: string;
  onUsernameSet: (username: string) => void;
  error?: string | null;
}

const UsernameSetup: React.FC<UsernameSetupProps> = ({
  currentUsername,
  onUsernameSet,
  error
}) => {
  const [username, setUsername] = useState(currentUsername);
  const [isValidating, setIsValidating] = useState(false);

  // التحقق من صحة اسم المستخدم
  const isValid = chatUtils.isValidUsername(username);
  const trimmedUsername = username.trim();

  // إنشاء اسم عشوائي
  const generateRandomUsername = () => {
    const randomName = chatUtils.getRandomUsername();
    setUsername(randomName);
  };

  // تأكيد اسم المستخدم
  const handleConfirm = async () => {
    if (!isValid) return;

    setIsValidating(true);
    
    try {
      // تأخير قصير لمحاكاة التحقق
      await new Promise(resolve => setTimeout(resolve, 500));
      onUsernameSet(trimmedUsername);
    } catch (err) {
      console.error('خطأ في تعيين اسم المستخدم:', err);
    } finally {
      setIsValidating(false);
    }
  };

  // معالجة الضغط على Enter
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && isValid) {
      handleConfirm();
    }
  };

  return (
    <div className="max-w-md mx-auto bg-gray-900/80 backdrop-blur-sm rounded-xl p-8 border border-gray-700/50 shadow-2xl">
      {/* العنوان */}
      <div className="text-center mb-8">
        <div className="w-16 h-16 bg-amber-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
          <User className="w-8 h-8 text-amber-400" />
        </div>
        <h2 className="text-2xl font-bold text-white mb-2">مرحباً بك في الدردشة!</h2>
        <p className="text-gray-400">اختر اسم المستخدم للبدء</p>
      </div>

      {/* حقل اسم المستخدم */}
      <div className="space-y-4">
        <div className="relative">
          <input
            type="text"
            value={username}
            onChange={(e) => setUsername(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="اسم المستخدم"
            className="w-full bg-gray-800/50 border border-gray-600/50 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-amber-500/50 focus:border-amber-500/50 transition-all duration-200"
            maxLength={20}
            disabled={isValidating}
          />
          
          {/* مؤشر الصحة */}
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
            {trimmedUsername && (
              isValid ? (
                <Check className="w-5 h-5 text-green-400" />
              ) : (
                <AlertCircle className="w-5 h-5 text-red-400" />
              )
            )}
          </div>
        </div>

        {/* رسائل التحقق */}
        <div className="text-sm space-y-1">
          {trimmedUsername && !isValid && (
            <p className="text-red-400 flex items-center gap-2">
              <AlertCircle className="w-4 h-4" />
              اسم المستخدم يجب أن يكون 2-20 حرف (أحرف وأرقام فقط)
            </p>
          )}
          
          {error && (
            <p className="text-red-400 flex items-center gap-2">
              <AlertCircle className="w-4 h-4" />
              {error}
            </p>
          )}
          
          {isValid && (
            <p className="text-green-400 flex items-center gap-2">
              <Check className="w-4 h-4" />
              اسم المستخدم صحيح
            </p>
          )}
        </div>

        {/* الأزرار */}
        <div className="space-y-3">
          <Button
            onClick={handleConfirm}
            disabled={!isValid || isValidating}
            className="w-full bg-amber-500 hover:bg-amber-600 text-black font-semibold py-3 rounded-lg transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
          >
            {isValidating ? (
              <>
                <div className="w-4 h-4 border-2 border-black border-t-transparent rounded-full animate-spin ml-2"></div>
                جاري التحقق...
              </>
            ) : (
              <>
                <Check className="w-4 h-4 ml-2" />
                بدء الدردشة
              </>
            )}
          </Button>

          <Button
            onClick={generateRandomUsername}
            variant="outline"
            disabled={isValidating}
            className="w-full border-gray-600 text-gray-300 hover:bg-gray-700/50 py-3 rounded-lg transition-all duration-200"
          >
            <Shuffle className="w-4 h-4 ml-2" />
            اسم عشوائي
          </Button>
        </div>

        {/* معلومات إضافية */}
        <div className="text-xs text-gray-500 text-center space-y-1">
          <p>• لا حاجة لتسجيل الدخول</p>
          <p>• اسم المستخدم مؤقت لهذه الجلسة</p>
          <p>• يمكنك تغييره في أي وقت</p>
        </div>
      </div>
    </div>
  );
};

export default UsernameSetup;
