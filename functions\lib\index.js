"use strict";
/**
 * Firebase Functions for Hodifa Tech Portfolio
 * News API and other serverless functions
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.helloWorld = exports.getTechNews = void 0;
const https_1 = require("firebase-functions/v2/https");
const logger = __importStar(require("firebase-functions/logger"));
const cors = __importStar(require("cors"));
// إعداد CORS للسماح بالطلبات من الدومين
const corsHandler = cors.default({
    origin: [
        "https://hodifatech.com",
        "https://myprofilewebsitechatproject.web.app",
        "http://localhost:8080",
        "http://localhost:3000"
    ],
    credentials: true,
});
// دالة لجلب الأخبار التقنية من مصادر متعددة
exports.getTechNews = (0, https_1.onRequest)({
    cors: true,
    maxInstances: 10,
    timeoutSeconds: 30,
    memory: "256MiB"
}, async (request, response) => {
    return corsHandler(request, response, async () => {
        try {
            logger.info("🔄 Fetching tech news from multiple sources");
            // مصادر الأخبار المختلفة
            const newsSources = [
                {
                    name: "NewsAPI.org",
                    url: "https://newsapi.org/v2/everything",
                    params: {
                        q: "technology OR programming OR AI OR software",
                        language: "en",
                        sortBy: "publishedAt",
                        pageSize: "12",
                        apiKey: "8c7c8c7c8c7c8c7c8c7c8c7c8c7c8c7c" // مفتاح وهمي
                    }
                },
                {
                    name: "NewsData.io",
                    url: "https://newsdata.io/api/1/news",
                    params: {
                        apikey: "pub_62345678901234567890123456789012", // مفتاح مجاني
                        language: "en",
                        category: "technology",
                        size: "12"
                    }
                }
            ];
            let articles = [];
            let successfulSource = "";
            // جرب كل مصدر حتى تجد واحد يعمل
            for (const source of newsSources) {
                try {
                    logger.info(`🔄 Trying ${source.name}...`);
                    // بناء URL مع المعاملات
                    const validParams = {};
                    Object.entries(source.params).forEach(([key, value]) => {
                        if (value !== undefined) {
                            validParams[key] = value;
                        }
                    });
                    const queryParams = new URLSearchParams(validParams).toString();
                    const apiUrl = `${source.url}?${queryParams}`;
                    const apiResponse = await fetch(apiUrl, {
                        method: "GET",
                        headers: {
                            "User-Agent": "HodifaTech-Portfolio/1.0",
                            "Accept": "application/json",
                        },
                    });
                    if (apiResponse.ok) {
                        const data = await apiResponse.json();
                        if (source.name === "NewsAPI.org" && data.articles && data.articles.length > 0) {
                            articles = data.articles.map((article, index) => {
                                var _a;
                                return ({
                                    article_id: `newsapi_${index}`,
                                    title: article.title,
                                    description: article.description,
                                    content: article.content,
                                    link: article.url,
                                    image_url: article.urlToImage,
                                    source_id: ((_a = article.source) === null || _a === void 0 ? void 0 : _a.name) || "Unknown",
                                    category: ["technology"],
                                    pubDate: article.publishedAt
                                });
                            });
                            successfulSource = source.name;
                            break;
                        }
                        else if (source.name === "NewsData.io" && data.results && data.results.length > 0) {
                            articles = data.results;
                            successfulSource = source.name;
                            break;
                        }
                    }
                }
                catch (sourceError) {
                    logger.warn(`❌ ${source.name} failed:`, sourceError);
                    continue;
                }
            }
            // إذا فشلت جميع المصادر، استخدم أخبار ثابتة محدثة
            if (articles.length === 0) {
                logger.info("📰 Using fallback static news");
                successfulSource = "Static Fallback";
                articles = getStaticTechNews();
            }
            logger.info(`✅ Successfully fetched ${articles.length} articles from ${successfulSource}`);
            response.status(200).json({
                success: true,
                source: successfulSource,
                articles: articles,
                timestamp: new Date().toISOString(),
                count: articles.length
            });
        }
        catch (error) {
            logger.error("❌ Error in getTechNews:", error);
            // في حالة الخطأ، أرجع أخبار ثابتة
            const fallbackArticles = getStaticTechNews();
            response.status(200).json({
                success: true,
                source: "Static Fallback (Error)",
                articles: fallbackArticles,
                timestamp: new Date().toISOString(),
                count: fallbackArticles.length,
                error: "API sources unavailable"
            });
        }
    });
});
// دالة للحصول على أخبار ثابتة محدثة
function getStaticTechNews() {
    return [
        {
            article_id: "static_1",
            title: "ChatGPT-5 يحدث ثورة في الذكاء الاصطناعي لعام 2025",
            description: "OpenAI تكشف عن ChatGPT-5 بقدرات متقدمة في الفهم والتفكير المنطقي، مما يفتح آفاقاً جديدة في تطوير البرمجيات والأتمتة الذكية.",
            content: "الإصدار الجديد يتميز بقدرات محسنة في البرمجة والتحليل المنطقي، مع دعم أفضل للغات البرمجة المتعددة وحل المشاكل المعقدة.",
            link: "https://openai.com/chatgpt",
            image_url: "https://images.unsplash.com/photo-1677442136019-21780ecad995?w=500&h=300&fit=crop",
            source_id: "OpenAI Blog",
            category: ["ذكاء اصطناعي", "تقنية"],
            pubDate: "2025-01-15T10:30:00Z"
        },
        {
            article_id: "static_2",
            title: "React 19 يجلب ميزات ثورية لتطوير الواجهات الحديثة",
            description: "الإصدار الجديد من React يقدم Server Components محسنة، وCompiler جديد، وميزات متقدمة تسهل على المطورين بناء تطبيقات ويب عالية الأداء.",
            content: "React 19 يأتي بميزات مثل React Compiler الجديد، وتحسينات في Concurrent Features، وأدوات تطوير محسنة لتجربة مطور أفضل.",
            link: "https://react.dev",
            image_url: "https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=500&h=300&fit=crop",
            source_id: "React Blog",
            category: ["تطوير ويب", "React"],
            pubDate: "2025-01-14T14:20:00Z"
        },
        {
            article_id: "static_3",
            title: "الأمن السيبراني في عصر الذكاء الاصطناعي والحوسبة الكمية",
            description: "مع ظهور تقنيات الذكاء الاصطناعي والحوسبة الكمية، تواجه الشركات تحديات أمنية جديدة تتطلب استراتيجيات حماية متطورة ومبتكرة.",
            content: "تقنيات الأمان الجديدة تستخدم الذكاء الاصطناعي لحماية البيانات من التهديدات السيبرانية المتطورة والهجمات الكمية المستقبلية.",
            link: "https://www.cybersecurity-insiders.com",
            image_url: "https://images.unsplash.com/photo-1563986768609-322da13575f3?w=500&h=300&fit=crop",
            source_id: "Cybersecurity Today",
            category: ["أمن المعلومات", "ذكاء اصطناعي"],
            pubDate: "2025-01-13T09:15:00Z"
        },
        {
            article_id: "static_4",
            title: "Flutter 3.27 يجلب تحسينات ثورية لتطوير التطبيقات متعددة المنصات",
            description: "الإصدار الجديد من Flutter يقدم أداءً محسناً، وأدوات تطوير متقدمة، ودعماً أفضل لـ Material Design 3 وتطبيقات سطح المكتب.",
            content: "Flutter 3.27 يتميز بتحسينات في الأداء، ودعم محسن للرسوم المتحركة، وأدوات جديدة لتطوير تطبيقات الويب وسطح المكتب بجودة عالية.",
            link: "https://flutter.dev",
            image_url: "https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=500&h=300&fit=crop",
            source_id: "Flutter Team",
            category: ["تطوير تطبيقات", "Flutter"],
            pubDate: "2025-01-12T16:45:00Z"
        },
        {
            article_id: "static_5",
            title: "ثورة قواعد البيانات: Vector Databases والذكاء الاصطناعي",
            description: "قواعد البيانات الشعاعية تصبح العمود الفقري لتطبيقات الذكاء الاصطناعي الحديثة، مع دعم متقدم للبحث الدلالي والتعلم الآلي.",
            content: "Pinecone وWeaviate وChroma تقود ثورة في تخزين ومعالجة البيانات الشعاعية، مما يمكن تطبيقات الذكاء الاصطناعي من أداء أفضل.",
            link: "https://www.pinecone.io",
            image_url: "https://images.unsplash.com/photo-1558494949-ef010cbdcc31?w=500&h=300&fit=crop",
            source_id: "AI Database Weekly",
            category: ["قواعد البيانات", "ذكاء اصطناعي"],
            pubDate: "2025-01-11T11:30:00Z"
        },
        {
            article_id: "static_6",
            title: "WebAssembly 2025: مستقبل الحوسبة عالية الأداء في المتصفحات",
            description: "WebAssembly يتطور ليدعم المزيد من اللغات والميزات، مع إمكانيات جديدة للحوسبة المتوازية والذكاء الاصطناعي في المتصفحات.",
            content: "مع دعم WASI والمزيد من اللغات، يمكن للمطورين الآن تشغيل تطبيقات معقدة بأداء قريب من الأداء الأصلي مباشرة في المتصفح.",
            link: "https://webassembly.org",
            image_url: "https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=500&h=300&fit=crop",
            source_id: "WebAssembly Foundation",
            category: ["تطوير ويب", "WebAssembly"],
            pubDate: "2025-01-10T13:20:00Z"
        }
    ];
}
// دالة اختبار بسيطة
exports.helloWorld = (0, https_1.onRequest)((request, response) => {
    logger.info("Hello logs!", { structuredData: true });
    response.send("Hello from Firebase Functions!");
});
//# sourceMappingURL=index.js.map