/**
 * Firebase Functions for Hodifa Tech Portfolio
 * News API and other serverless functions
 */

import {onRequest} from "firebase-functions/v2/https";
import * as logger from "firebase-functions/logger";
import * as cors from "cors";
import {defineSecret} from "firebase-functions/params";

// تعريف المتغيرات السرية
const newsApiKey = defineSecret("NEWSAPI_KEY");

// إعداد CORS للسماح بالطلبات من الدومين
const corsHandler = cors.default({
  origin: [
    "https://hodifatech.com",
    "https://myprofilewebsitechatproject.web.app",
    "https://myprofilewebsitechatproject.firebaseapp.com",
    "http://localhost:8080",
    "http://localhost:3000",
    "http://localhost:5173"
  ],
  credentials: true,
});

// دالة لجلب الأخبار التقنية باللغة العربية والإنجليزية
export const getTechNews = onRequest({
  cors: true,
  maxInstances: 10,
  timeoutSeconds: 30,
  memory: "256MiB",
  secrets: [newsApiKey]
}, async (request, response) => {
  return corsHandler(request, response, async () => {
    try {
      logger.info("🔄 Fetching tech news from multiple sources");

      // الحصول على مفتاح API من متغيرات البيئة
      const API_KEY = newsApiKey.value() || process.env.NEWSAPI_KEY || "********************************";

      // مصادر الأخبار المختلفة - التركيز على الأخبار العربية
      const newsSources = [
        {
          name: "NewsAPI.org - Arabic Tech Primary",
          url: "https://newsapi.org/v2/everything",
          params: {
            q: "تقنية OR تكنولوجيا OR ذكاء اصطناعي",
            language: "ar",
            sortBy: "publishedAt",
            pageSize: "10",
            apiKey: API_KEY
          }
        },
        {
          name: "NewsAPI.org - Arabic Tech Secondary",
          url: "https://newsapi.org/v2/everything",
          params: {
            q: "برمجة OR تطوير OR حاسوب OR إنترنت",
            language: "ar",
            sortBy: "publishedAt",
            pageSize: "6",
            apiKey: API_KEY
          }
        },
        {
          name: "NewsAPI.org - English Tech Fallback",
          url: "https://newsapi.org/v2/everything",
          params: {
            q: "technology OR programming OR AI OR software",
            language: "en",
            sortBy: "publishedAt",
            pageSize: "4",
            apiKey: API_KEY
          }
        }
      ];

      let articles: Array<Record<string, unknown>> = [];
      let successfulSource = "";

      // جرب كل مصدر حتى تجد واحد يعمل
      for (const source of newsSources) {
        try {
          logger.info(`🔄 Trying ${source.name}...`);

          // بناء URL مع المعاملات
          const validParams: Record<string, string> = {};
          Object.entries(source.params).forEach(([key, value]) => {
            if (value !== undefined) {
              validParams[key] = value;
            }
          });
          const queryParams = new URLSearchParams(validParams).toString();
          const apiUrl = `${source.url}?${queryParams}`;

          const apiResponse = await fetch(apiUrl, {
            method: "GET",
            headers: {
              "User-Agent": "HodifaTech-Portfolio/1.0",
              "Accept": "application/json",
            },
          });

          if (apiResponse.ok) {
            const data = await apiResponse.json();

            if (data.articles && data.articles.length > 0) {
              const processedArticles = data.articles
                .filter((article: Record<string, unknown>) =>
                  article.title &&
                  article.description &&
                  article.url &&
                  !String(article.title).toLowerCase().includes('[removed]') &&
                  !String(article.description).toLowerCase().includes('[removed]')
                )
                .map((article: Record<string, unknown>, index: number) => ({
                  article_id: `${source.name.toLowerCase().replace(/[^a-z0-9]/g, '_')}_${index}`,
                  title: article.title,
                  description: article.description,
                  content: article.content,
                  link: article.url,
                  image_url: article.urlToImage,
                  source_id: (article.source as Record<string, unknown>)?.name || source.name,
                  category: ["technology"],
                  pubDate: article.publishedAt,
                  language: source.params.language
                }));

              if (processedArticles.length > 0) {
                articles = [...articles, ...processedArticles];
                successfulSource = successfulSource ? `${successfulSource}, ${source.name}` : source.name;
                logger.info(`✅ ${source.name} returned ${processedArticles.length} articles`);
              }
            }
          } else {
            logger.warn(`❌ ${source.name} API call failed with status: ${apiResponse.status}`);
          }
        } catch (sourceError) {
          logger.warn(`❌ ${source.name} failed:`, sourceError);
          continue;
        }
      }

      // ترتيب المقالات حسب التاريخ وأخذ أحدث 12 مقال
      articles = articles
        .sort((a, b) => new Date(String(b.pubDate)).getTime() - new Date(String(a.pubDate)).getTime())
        .slice(0, 12);

      // إذا فشلت جميع المصادر، استخدم أخبار ثابتة محدثة
      if (articles.length === 0) {
        logger.info("📰 Using fallback static news");
        successfulSource = "Static Fallback";
        articles = getStaticTechNews();
      }

      logger.info(`✅ Successfully fetched ${articles.length} articles from ${successfulSource}`);

      response.status(200).json({
        success: true,
        source: successfulSource,
        articles: articles,
        timestamp: new Date().toISOString(),
        count: articles.length
      });

    } catch (error) {
      logger.error("❌ Error in getTechNews:", error);

      // في حالة الخطأ، أرجع أخبار ثابتة
      const fallbackArticles = getStaticTechNews();

      response.status(200).json({
        success: true,
        source: "Static Fallback (Error)",
        articles: fallbackArticles,
        timestamp: new Date().toISOString(),
        count: fallbackArticles.length,
        error: "API sources unavailable"
      });
    }
  });
});

// دالة للحصول على أخبار ثابتة محدثة (تركيز على المحتوى العربي)
function getStaticTechNews() {
  return [
    {
      article_id: "static_ar_1",
      title: "الذكاء الاصطناعي يغير مستقبل التقنية في العالم العربي",
      description: "تشهد المنطقة العربية نمواً متسارعاً في تطبيقات الذكاء الاصطناعي، مع استثمارات ضخمة في التقنيات الحديثة والابتكار التكنولوجي.",
      content: "الدول العربية تستثمر بقوة في تقنيات الذكاء الاصطناعي لتطوير الخدمات الحكومية والقطاعات الاقتصادية المختلفة.",
      link: "https://www.ai-arabia.com",
      image_url: "https://images.unsplash.com/photo-1677442136019-21780ecad995?w=500&h=300&fit=crop",
      source_id: "تقنية عربية",
      category: ["ذكاء اصطناعي", "تقنية"],
      pubDate: "2025-01-15T10:30:00Z",
      language: "ar"
    },
    {
      article_id: "static_en_1",
      title: "React 19 Brings Revolutionary Features for Modern UI Development",
      description: "The new React version introduces enhanced Server Components, a new Compiler, and advanced features that make it easier for developers to build high-performance web applications.",
      content: "React 19 comes with features like the new React Compiler, improvements in Concurrent Features, and enhanced development tools for a better developer experience.",
      link: "https://react.dev",
      image_url: "https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=500&h=300&fit=crop",
      source_id: "React Blog",
      category: ["web development", "React"],
      pubDate: "2025-01-14T14:20:00Z",
      language: "en"
    },
    {
      article_id: "static_ar_2",
      title: "تطوير تطبيقات الهواتف الذكية باستخدام React Native في المنطقة العربية",
      description: "يشهد سوق تطوير التطبيقات في العالم العربي نمواً كبيراً، مع تزايد الطلب على تطبيقات الهواتف الذكية المطورة بتقنيات حديثة مثل React Native.",
      content: "المطورون العرب يتجهون بقوة نحو استخدام React Native لتطوير تطبيقات متعددة المنصات بكفاءة عالية وتكلفة أقل.",
      link: "https://www.react-native-arabia.com",
      image_url: "https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=500&h=300&fit=crop",
      source_id: "تطوير عربي",
      category: ["تطوير تطبيقات", "برمجة"],
      pubDate: "2025-01-14T09:15:00Z",
      language: "ar"
    },
    {
      article_id: "static_ar_3",
      title: "الأمن السيبراني في عصر الذكاء الاصطناعي والحوسبة الكمية",
      description: "مع ظهور تقنيات الذكاء الاصطناعي والحوسبة الكمية، تواجه الشركات تحديات أمنية جديدة تتطلب استراتيجيات حماية متطورة ومبتكرة.",
      content: "تقنيات الأمان الجديدة تستخدم الذكاء الاصطناعي لحماية البيانات من التهديدات السيبرانية المتطورة والهجمات الكمية المستقبلية.",
      link: "https://www.cybersecurity-arabia.com",
      image_url: "https://images.unsplash.com/photo-1563986768609-322da13575f3?w=500&h=300&fit=crop",
      source_id: "أمن المعلومات العربي",
      category: ["أمن المعلومات", "ذكاء اصطناعي"],
      pubDate: "2025-01-13T09:15:00Z",
      language: "ar"
    },
    {
      article_id: "static_en_2",
      title: "Flutter 3.27 Brings Revolutionary Improvements for Cross-Platform Development",
      description: "The new Flutter version offers enhanced performance, advanced development tools, and better support for Material Design 3 and desktop applications.",
      content: "Flutter 3.27 features performance improvements, enhanced animation support, and new tools for developing high-quality web and desktop applications.",
      link: "https://flutter.dev",
      image_url: "https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=500&h=300&fit=crop",
      source_id: "Flutter Team",
      category: ["app development", "Flutter"],
      pubDate: "2025-01-12T16:45:00Z",
      language: "en"
    },
    {
      article_id: "static_ar_4",
      title: "تقنيات البلوك تشين والعملات الرقمية في الشرق الأوسط",
      description: "تشهد منطقة الشرق الأوسط اهتماماً متزايداً بتقنيات البلوك تشين والعملات الرقمية، مع إطلاق مشاريع رائدة في هذا المجال.",
      content: "الإمارات والسعودية تقودان المنطقة في تبني تقنيات البلوك تشين لتطوير الخدمات الحكومية والمالية.",
      link: "https://www.blockchain-arabia.com",
      image_url: "https://images.unsplash.com/photo-1639762681485-074b7f938ba0?w=500&h=300&fit=crop",
      source_id: "بلوك تشين عربي",
      category: ["بلوك تشين", "تقنية مالية"],
      pubDate: "2025-01-12T11:30:00Z",
      language: "ar"
    },
    {
      article_id: "static_ar_5",
      title: "ثورة قواعد البيانات: Vector Databases والذكاء الاصطناعي",
      description: "قواعد البيانات الشعاعية تصبح العمود الفقري لتطبيقات الذكاء الاصطناعي الحديثة، مع دعم متقدم للبحث الدلالي والتعلم الآلي.",
      content: "تقنيات جديدة في قواعد البيانات تمكن من تطوير تطبيقات ذكية أكثر فعالية في المنطقة العربية.",
      link: "https://www.database-arabia.com",
      image_url: "https://images.unsplash.com/photo-1558494949-ef010cbdcc31?w=500&h=300&fit=crop",
      source_id: "قواعد البيانات العربية",
      category: ["قواعد البيانات", "ذكاء اصطناعي"],
      pubDate: "2025-01-11T11:30:00Z",
      language: "ar"
    },
    {
      article_id: "static_en_3",
      title: "WebAssembly 2025: The Future of High-Performance Computing in Browsers",
      description: "WebAssembly evolves to support more languages and features, with new capabilities for parallel computing and AI in browsers.",
      content: "With WASI support and more languages, developers can now run complex applications with near-native performance directly in the browser.",
      link: "https://webassembly.org",
      image_url: "https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=500&h=300&fit=crop",
      source_id: "WebAssembly Foundation",
      category: ["web development", "WebAssembly"],
      pubDate: "2025-01-10T13:20:00Z",
      language: "en"
    }
  ];
}

// دالة اختبار بسيطة
export const helloWorld = onRequest((_request, response) => {
  logger.info("Hello logs!", {structuredData: true});
  response.send("Hello from Firebase Functions!");
});
