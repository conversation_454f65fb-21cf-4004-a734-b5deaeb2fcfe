# Cloudflare Worker Configuration
# للأخبار التقنية العربية - مجاني 100%

name = "arabic-tech-news-proxy"
main = "news-proxy.js"
compatibility_date = "2024-01-01"

# إعدادات Worker
[env.production]
name = "arabic-tech-news-proxy"

# متغيرات البيئة (يجب إضافة NEWS_API_KEY عبر Dashboard)
[vars]
ENVIRONMENT = "production"

# إعدادات الأمان
[triggers]
crons = []

# حدود الاستخدام (مجاني)
# 100,000 requests/day
# 10ms CPU time per request
