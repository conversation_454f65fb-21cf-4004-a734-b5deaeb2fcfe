/**
 * مكون عرض رسالة واحدة في الدردشة
 */

import React from 'react';
import { ChatMessage as ChatMessageType, chatUtils } from '@/lib/supabase';
import { User, Clock } from 'lucide-react';

interface ChatMessageProps {
  message: ChatMessageType;
  isOwnMessage?: boolean;
  showAvatar?: boolean;
}

const ChatMessage: React.FC<ChatMessageProps> = ({ 
  message, 
  isOwnMessage = false,
  showAvatar = true 
}) => {
  const formattedTime = chatUtils.formatMessageTime(message.created_at);
  const userColor = message.user_color || '#6B7280';

  return (
    <div className={`flex items-start gap-3 p-3 rounded-lg transition-all duration-200 hover:bg-gray-800/30 ${
      isOwnMessage ? 'flex-row-reverse bg-amber-500/10' : ''
    }`}>
      {/* صورة المستخدم */}
      {showAvatar && (
        <div 
          className="flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-bold shadow-lg"
          style={{ backgroundColor: userColor }}
        >
          <User className="w-4 h-4" />
        </div>
      )}

      {/* محتوى الرسالة */}
      <div className={`flex-1 min-w-0 ${isOwnMessage ? 'text-right' : 'text-left'}`}>
        {/* اسم المستخدم والوقت */}
        <div className={`flex items-center gap-2 mb-1 ${isOwnMessage ? 'justify-end' : 'justify-start'}`}>
          <span 
            className="font-semibold text-sm truncate"
            style={{ color: userColor }}
          >
            {message.username}
          </span>
          <div className="flex items-center gap-1 text-xs text-gray-400">
            <Clock className="w-3 h-3" />
            <span>{formattedTime}</span>
          </div>
        </div>

        {/* نص الرسالة */}
        <div className={`text-gray-200 text-sm leading-relaxed break-words ${
          isOwnMessage ? 'bg-amber-500/20 border border-amber-500/30' : 'bg-gray-700/50'
        } rounded-lg px-3 py-2`}>
          {message.message}
        </div>
      </div>
    </div>
  );
};

export default ChatMessage;
