/**
 * مكون قائمة الرسائل في الدردشة
 */

import React, { useEffect, useRef } from 'react';
import { ChatMessage as ChatMessageType } from '@/lib/supabase';
import ChatMessage from './ChatMessage';
import { MessageCircle, Wifi, WifiOff, Loader2 } from 'lucide-react';

interface ChatMessageListProps {
  messages: ChatMessageType[];
  currentUsername: string;
  isLoading: boolean;
  isConnected: boolean;
  error: string | null;
  onRetry?: () => void;
}

const ChatMessageList: React.FC<ChatMessageListProps> = ({
  messages,
  currentUsername,
  isLoading,
  isConnected,
  error,
  onRetry
}) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // التمرير التلقائي للأسفل عند وصول رسائل جديدة
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // عرض حالة التحميل
  if (isLoading) {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-900/50 rounded-lg">
        <div className="text-center">
          <Loader2 className="w-8 h-8 text-amber-400 animate-spin mx-auto mb-4" />
          <p className="text-gray-400">جاري تحميل الرسائل...</p>
        </div>
      </div>
    );
  }

  // عرض حالة الخطأ
  if (error) {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-900/50 rounded-lg">
        <div className="text-center max-w-md">
          <WifiOff className="w-12 h-12 text-red-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-red-400 mb-2">خطأ في الاتصال</h3>
          <p className="text-gray-400 mb-4">{error}</p>
          {onRetry && (
            <button
              onClick={onRetry}
              className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors"
            >
              إعادة المحاولة
            </button>
          )}
        </div>
      </div>
    );
  }

  // عرض رسالة فارغة
  if (messages.length === 0) {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-900/50 rounded-lg">
        <div className="text-center">
          <MessageCircle className="w-16 h-16 text-gray-600 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-400 mb-2">لا توجد رسائل بعد</h3>
          <p className="text-gray-500">كن أول من يبدأ المحادثة!</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col bg-gray-900/50 rounded-lg">
      {/* شريط الحالة */}
      <div className="flex items-center justify-between p-3 border-b border-gray-700/50">
        <div className="flex items-center gap-2">
          {isConnected ? (
            <>
              <Wifi className="w-4 h-4 text-green-400" />
              <span className="text-sm text-green-400">متصل</span>
            </>
          ) : (
            <>
              <WifiOff className="w-4 h-4 text-red-400" />
              <span className="text-sm text-red-400">غير متصل</span>
            </>
          )}
        </div>
        
        <div className="text-sm text-gray-400">
          {messages.length} رسالة
        </div>
      </div>

      {/* قائمة الرسائل */}
      <div 
        ref={containerRef}
        className="flex-1 overflow-y-auto p-2 space-y-1 scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800"
        style={{ maxHeight: '400px' }}
      >
        {messages.map((message, index) => {
          const isOwnMessage = message.username === currentUsername;
          const prevMessage = index > 0 ? messages[index - 1] : null;
          const showAvatar = !prevMessage || prevMessage.username !== message.username;

          return (
            <ChatMessage
              key={message.id}
              message={message}
              isOwnMessage={isOwnMessage}
              showAvatar={showAvatar}
            />
          );
        })}
        
        {/* مرجع للتمرير التلقائي */}
        <div ref={messagesEndRef} />
      </div>

      {/* مؤشر الكتابة (يمكن إضافته لاحقاً) */}
      {isConnected && (
        <div className="p-2 border-t border-gray-700/50">
          <div className="flex items-center gap-2 text-xs text-gray-500">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <span>الدردشة نشطة</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default ChatMessageList;
