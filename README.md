# 🌟 الموقع الشخصي لحذيفة عبدالمعز

[![Deploy to GitHub Pages](https://github.com/HA1234098765/hodifa-portfolio/actions/workflows/deploy.yml/badge.svg)](https://github.com/HA1234098765/hodifa-portfolio/actions/workflows/deploy.yml)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![React](https://img.shields.io/badge/React-18.0+-blue.svg)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0+-blue.svg)](https://www.typescriptlang.org/)
[![Vite](https://img.shields.io/badge/Vite-5.0+-646CFF.svg)](https://vitejs.dev/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-3.0+-38B2AC.svg)](https://tailwindcss.com/)

الموقع الشخصي لحذيفة عبدالمعز الحذيفي - مهندس تقنية معلومات ومطور ويب متخصص في Laravel وتطوير التطبيقات.

## 🔗 الروابط المباشرة

- 🌐 **الموقع المباشر:** [hodifa-portfolio.com](https://ha1234098765.github.io/hodifa-portfolio/)
- 📱 **معاينة الجوال:** [Mobile Preview](https://ha1234098765.github.io/hodifa-portfolio/)
- 📰 **الأخبار التقنية:** [Tech News](https://ha1234098765.github.io/hodifa-portfolio/tech-news)
- 💼 **المشاريع:** [Projects](https://ha1234098765.github.io/hodifa-portfolio/projects)

## 📸 لقطات الشاشة

### الصفحة الرئيسية
![الصفحة الرئيسية](https://via.placeholder.com/800x400/1a1a1a/f59e0b?text=الصفحة+الرئيسية)

### صفحة المشاريع
![صفحة المشاريع](https://via.placeholder.com/800x400/1a1a1a/f59e0b?text=صفحة+المشاريع)

### الأخبار التقنية
![الأخبار التقنية](https://via.placeholder.com/800x400/1a1a1a/f59e0b?text=الأخبار+التقنية)

## 🚀 التقنيات المستخدمة

- **React 18** مع TypeScript للتطوير الآمن والقوي
- **Vite** لسرعة التطوير والبناء
- **Tailwind CSS** للتصميم السريع والمرن
- **ShadCN/UI** مكتبة مكونات UI متقدمة
- **React Router** للتنقل بين الصفحات
- **React Query** لإدارة البيانات والـ API
- **Lucide React** للأيقونات الجميلة

## 📋 الميزات

- ✅ تصميم متجاوب (Responsive Design)
- ✅ دعم الوضع المظلم/الفاتح
- ✅ دعم كامل للغة العربية (RTL)
- ✅ رسوم متحركة متقنة
- ✅ تحسين محركات البحث (SEO)
- ✅ نظام أخبار تقنية مجاني 100% مع Firebase Functions
- ✅ معرض أعمال تفاعلي
- ✅ نموذج تواصل متقدم

## 📰 نظام الأخبار التقنية الجديد

### 🎯 مميزات النظام
- **مجاني 100%** - لا توجد تكاليف إضافية
- **أخبار حقيقية** من NewsAPI.org
- **دعم اللغتين** العربية والإنجليزية
- **Firebase Functions** كوسيط آمن لحماية مفتاح API
- **أخبار احتياطية** عالية الجودة
- **تحديث تلقائي** مع React Query

### 🔧 التقنيات المستخدمة
- **Firebase Functions** (TypeScript) - وسيط آمن
- **NewsAPI.org** - مصدر الأخبار الحقيقية
- **React Query** - التخزين المؤقت والتحديث
- **Tailwind CSS** - تصميم متجاوب

### 📊 مصادر الأخبار
- أخبار تقنية باللغة العربية: `تكنولوجيا، تقنية، ذكاء اصطناعي، برمجة`
- أخبار تقنية باللغة الإنجليزية: `technology, programming, AI, software`
- أخبار احتياطية محدثة في حالة عدم توفر الاتصال

### 🚀 كيفية العمل
1. **React App** ترسل طلب إلى Firebase Function
2. **Firebase Function** تجلب الأخبار من NewsAPI.org
3. **معالجة البيانات** وتنسيقها
4. **إرجاع النتائج** أو الأخبار الاحتياطية

## 🛠️ التثبيت والتشغيل

### المتطلبات
- Node.js (الإصدار 18 أو أحدث)
- npm أو yarn

### خطوات التثبيت

```bash
# 1. استنساخ المستودع
git clone https://github.com/HA1234098765/hodifa-portfolio.git

# 2. الانتقال إلى مجلد المشروع
cd hodifa-portfolio

# 3. تثبيت التبعيات
npm install

# 4. تشغيل الخادم المحلي
npm run dev
```

### البناء للإنتاج

```bash
# بناء المشروع للإنتاج
npm run build

# معاينة البناء محلياً
npm run preview
```

## 📁 هيكل المشروع

```
src/
├── components/          # المكونات المشتركة
│   ├── ui/             # مكونات ShadCN UI
│   ├── Navigation.tsx  # شريط التنقل
│   └── Footer.tsx      # تذييل الصفحة
├── pages/              # صفحات التطبيق
│   ├── Index.tsx       # الصفحة الرئيسية
│   ├── About.tsx       # صفحة نبذة عني
│   ├── Projects.tsx    # صفحة المشاريع
│   ├── Contact.tsx     # صفحة التواصل
│   └── TechNews.tsx    # صفحة الأخبار التقنية
├── hooks/              # React Hooks مخصصة
├── lib/                # المكتبات والأدوات المساعدة
└── App.tsx             # المكون الرئيسي
```

## 🌐 النشر

المشروع مُعد للنشر على GitHub Pages مع النشر التلقائي:

### النشر التلقائي:
1. ادفع التغييرات إلى GitHub
2. سيتم النشر تلقائياً عبر GitHub Actions
3. الموقع سيكون متاح على: `https://username.github.io/hodifa-portfolio/`

### إعداد GitHub Pages:
1. اذهب إلى إعدادات المستودع
2. انتقل إلى قسم "Pages"
3. اختر "GitHub Actions" كمصدر
4. سيتم تفعيل النشر التلقائي

### النشر اليدوي:
```bash
npm run build
npm run deploy
```

## 📧 التواصل

- **البريد الإلكتروني:** <EMAIL>
- **الهاتف:** +967 777548421 | +967 718706242
- **LinkedIn:** [hodifa-al-hodify](https://www.linkedin.com/in/hodifa-al-hodify-30644b289)
- **GitHub:** [HA1234098765](https://github.com/HA1234098765)

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

---

**تم التطوير بـ ❤️ بواسطة حذيفة عبدالمعز الحذيفي**