#!/usr/bin/env node

/**
 * سكريبت اختبار المسارات
 * يتحقق من جميع المسارات ويتأكد من عملها بشكل صحيح
 */

import { execSync } from 'child_process';
import { fileURLToPath } from 'url';
import path from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// قائمة المسارات للاختبار
const testRoutes = [
  // المسارات الأساسية
  { path: '/', name: 'الصفحة الرئيسية', expected: 200 },
  { path: '/about', name: 'السيرة الذاتية', expected: 200 },
  { path: '/projects', name: 'المشاريع', expected: 200 },
  { path: '/contact', name: 'التواصل', expected: 200 },
  { path: '/tech-news', name: 'الأخبار التقنية', expected: 200 },
  
  // المسارات البديلة
  { path: '/About', name: 'السيرة الذاتية (كبير)', expected: 200 },
  { path: '/Projects', name: 'المشاريع (كبير)', expected: 200 },
  { path: '/Contact', name: 'التواصل (كبير)', expected: 200 },
  { path: '/TechNews', name: 'الأخبار التقنية (كبير)', expected: 200 },
  { path: '/technews', name: 'الأخبار التقنية (صغير)', expected: 200 },
  
  // مسارات الدردشة
  { path: '/group-chat', name: 'الدردشة الجماعية', expected: 200 },
  { path: '/chat', name: 'الدردشة', expected: 200 },
  
  // مسارات الاختبار
  { path: '/pdf-test', name: 'اختبار PDF', expected: 200 },
  
  // مسارات غير موجودة (يجب أن ترجع 404)
  { path: '/nonexistent', name: 'صفحة غير موجودة', expected: 404 },
  { path: '/old-page', name: 'صفحة قديمة', expected: 404 },
  { path: '/random-path', name: 'مسار عشوائي', expected: 404 }
];

// المسارات المحذوفة (يجب إعادة توجيهها)
const deprecatedRoutes = [
  { path: '/news', redirect: '/tech-news', name: 'أخبار قديمة' },
  { path: '/blog', redirect: '/tech-news', name: 'مدونة قديمة' },
  { path: '/portfolio', redirect: '/projects', name: 'محفظة قديمة' },
  { path: '/cv', redirect: '/about', name: 'سيرة ذاتية قديمة' }
];

async function testRoute(baseUrl, route) {
  try {
    const url = `${baseUrl}${route.path}`;
    console.log(`🔍 اختبار: ${route.name} (${url})`);
    
    const response = await fetch(url, {
      method: 'HEAD', // استخدام HEAD لتوفير البيانات
      headers: {
        'User-Agent': 'Route-Tester/1.0'
      }
    });

    const status = response.status;
    const success = status === route.expected;
    
    if (success) {
      console.log(`✅ ${route.name}: ${status} (متوقع: ${route.expected})`);
    } else {
      console.log(`❌ ${route.name}: ${status} (متوقع: ${route.expected})`);
    }
    
    return { ...route, actualStatus: status, success };
  } catch (error) {
    console.log(`💥 ${route.name}: خطأ - ${error.message}`);
    return { ...route, actualStatus: 'ERROR', success: false, error: error.message };
  }
}

async function testRedirect(baseUrl, redirect) {
  try {
    const url = `${baseUrl}${redirect.path}`;
    console.log(`🔄 اختبار إعادة توجيه: ${redirect.name} (${url})`);
    
    const response = await fetch(url, {
      method: 'HEAD',
      redirect: 'manual', // لا تتبع إعادة التوجيه تلقائياً
      headers: {
        'User-Agent': 'Route-Tester/1.0'
      }
    });

    const status = response.status;
    const location = response.headers.get('location');
    const success = (status === 301 || status === 302) && location && location.includes(redirect.redirect);
    
    if (success) {
      console.log(`✅ ${redirect.name}: ${status} → ${location}`);
    } else {
      console.log(`❌ ${redirect.name}: ${status} (متوقع: 301/302 → ${redirect.redirect})`);
    }
    
    return { ...redirect, actualStatus: status, location, success };
  } catch (error) {
    console.log(`💥 ${redirect.name}: خطأ - ${error.message}`);
    return { ...redirect, actualStatus: 'ERROR', success: false, error: error.message };
  }
}

async function runTests() {
  console.log('🚀 بدء اختبار المسارات...\n');
  console.log('=' .repeat(60));

  // تحديد URL الأساسي
  const baseUrl = process.env.TEST_URL || 'http://localhost:8080';
  console.log(`🌐 URL الأساسي: ${baseUrl}\n`);

  // اختبار المسارات العادية
  console.log('📋 اختبار المسارات الأساسية:');
  console.log('-' .repeat(40));
  
  const routeResults = [];
  for (const route of testRoutes) {
    const result = await testRoute(baseUrl, route);
    routeResults.push(result);
    await new Promise(resolve => setTimeout(resolve, 100)); // تأخير قصير
  }

  console.log('\n📋 اختبار إعادة التوجيه:');
  console.log('-' .repeat(40));
  
  const redirectResults = [];
  for (const redirect of deprecatedRoutes) {
    const result = await testRedirect(baseUrl, redirect);
    redirectResults.push(result);
    await new Promise(resolve => setTimeout(resolve, 100)); // تأخير قصير
  }

  // تقرير النتائج
  console.log('\n' + '=' .repeat(60));
  console.log('📊 تقرير النتائج:');
  console.log('=' .repeat(60));

  const totalRoutes = routeResults.length;
  const successfulRoutes = routeResults.filter(r => r.success).length;
  const failedRoutes = routeResults.filter(r => !r.success);

  const totalRedirects = redirectResults.length;
  const successfulRedirects = redirectResults.filter(r => r.success).length;
  const failedRedirects = redirectResults.filter(r => !r.success);

  console.log(`\n🎯 المسارات الأساسية:`);
  console.log(`   ✅ نجح: ${successfulRoutes}/${totalRoutes}`);
  console.log(`   ❌ فشل: ${failedRoutes.length}/${totalRoutes}`);

  console.log(`\n🔄 إعادة التوجيه:`);
  console.log(`   ✅ نجح: ${successfulRedirects}/${totalRedirects}`);
  console.log(`   ❌ فشل: ${failedRedirects.length}/${totalRedirects}`);

  // عرض الأخطاء
  if (failedRoutes.length > 0) {
    console.log(`\n❌ المسارات الفاشلة:`);
    failedRoutes.forEach(route => {
      console.log(`   - ${route.name}: ${route.actualStatus} (متوقع: ${route.expected})`);
    });
  }

  if (failedRedirects.length > 0) {
    console.log(`\n❌ إعادة التوجيه الفاشلة:`);
    failedRedirects.forEach(redirect => {
      console.log(`   - ${redirect.name}: ${redirect.actualStatus} (متوقع: 301/302)`);
    });
  }

  // النتيجة النهائية
  const totalTests = totalRoutes + totalRedirects;
  const totalSuccess = successfulRoutes + successfulRedirects;
  const successRate = ((totalSuccess / totalTests) * 100).toFixed(1);

  console.log(`\n🏆 النتيجة النهائية:`);
  console.log(`   📊 معدل النجاح: ${successRate}%`);
  console.log(`   ✅ نجح: ${totalSuccess}/${totalTests}`);
  console.log(`   ❌ فشل: ${totalTests - totalSuccess}/${totalTests}`);

  if (successRate >= 90) {
    console.log(`\n🎉 ممتاز! جميع المسارات تعمل بشكل صحيح`);
    process.exit(0);
  } else if (successRate >= 75) {
    console.log(`\n⚠️ جيد، لكن هناك بعض المشاكل تحتاج إصلاح`);
    process.exit(1);
  } else {
    console.log(`\n🚨 هناك مشاكل كثيرة تحتاج إصلاح فوري`);
    process.exit(1);
  }
}

// تشغيل الاختبارات
runTests().catch(error => {
  console.error('💥 خطأ في تشغيل الاختبارات:', error);
  process.exit(1);
});
