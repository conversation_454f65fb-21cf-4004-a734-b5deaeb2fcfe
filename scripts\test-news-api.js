#!/usr/bin/env node

/**
 * سكريبت اختبار نظام الأخبار التقنية
 * يختبر Firebase Function وNewsAPI
 */

const https = require('https');
const http = require('http');

// اختبار Firebase Function
async function testFirebaseFunction() {
  console.log('🔄 اختبار Firebase Function...');
  
  const functionUrl = 'https://gettechnews-myprofilewebsitechatproject.cloudfunctions.net/getTechNews';
  
  try {
    const response = await fetch(functionUrl);
    const data = await response.json();
    
    if (data.success && data.articles && data.articles.length > 0) {
      console.log('✅ Firebase Function تعمل بنجاح!');
      console.log(`📊 عدد المقالات: ${data.articles.length}`);
      console.log(`📡 المصدر: ${data.source}`);
      console.log(`🕒 الوقت: ${data.timestamp}`);
      
      // عرض أول مقال كمثال
      const firstArticle = data.articles[0];
      console.log('\n📰 مثال على مقال:');
      console.log(`العنوان: ${firstArticle.title}`);
      console.log(`المصدر: ${firstArticle.source_id}`);
      console.log(`اللغة: ${firstArticle.language || 'غير محدد'}`);
      
      return true;
    } else {
      console.log('⚠️ Firebase Function ترجع بيانات فارغة');
      return false;
    }
  } catch (error) {
    console.error('❌ خطأ في Firebase Function:', error.message);
    return false;
  }
}

// اختبار NewsAPI مباشرة
async function testNewsAPI() {
  console.log('\n🔄 اختبار NewsAPI مباشرة...');
  
  const API_KEY = '********************************';
  const apiUrl = `https://newsapi.org/v2/everything?q=technology&language=en&sortBy=publishedAt&pageSize=5&apiKey=${API_KEY}`;
  
  try {
    const response = await fetch(apiUrl);
    const data = await response.json();
    
    if (data.status === 'ok' && data.articles && data.articles.length > 0) {
      console.log('✅ NewsAPI يعمل بنجاح!');
      console.log(`📊 عدد المقالات: ${data.articles.length}`);
      console.log(`📊 إجمالي النتائج: ${data.totalResults}`);
      return true;
    } else {
      console.log('⚠️ NewsAPI لا يرجع بيانات صحيحة');
      console.log('الاستجابة:', data);
      return false;
    }
  } catch (error) {
    console.error('❌ خطأ في NewsAPI:', error.message);
    return false;
  }
}

// اختبار الاتصال بالإنترنت
async function testInternetConnection() {
  console.log('🔄 اختبار الاتصال بالإنترنت...');
  
  try {
    const response = await fetch('https://www.google.com', { 
      method: 'HEAD',
      timeout: 5000 
    });
    
    if (response.ok) {
      console.log('✅ الاتصال بالإنترنت يعمل بنجاح!');
      return true;
    } else {
      console.log('⚠️ مشكلة في الاتصال بالإنترنت');
      return false;
    }
  } catch (error) {
    console.error('❌ لا يوجد اتصال بالإنترنت:', error.message);
    return false;
  }
}

// تشغيل جميع الاختبارات
async function runAllTests() {
  console.log('🧪 بدء اختبار نظام الأخبار التقنية\n');
  console.log('=' .repeat(50));
  
  const results = {
    internet: await testInternetConnection(),
    newsapi: await testNewsAPI(),
    firebase: await testFirebaseFunction()
  };
  
  console.log('\n' + '=' .repeat(50));
  console.log('📋 ملخص النتائج:');
  console.log(`🌐 الاتصال بالإنترنت: ${results.internet ? '✅ يعمل' : '❌ لا يعمل'}`);
  console.log(`📡 NewsAPI: ${results.newsapi ? '✅ يعمل' : '❌ لا يعمل'}`);
  console.log(`🔥 Firebase Function: ${results.firebase ? '✅ يعمل' : '❌ لا يعمل'}`);
  
  const allWorking = Object.values(results).every(result => result);
  
  if (allWorking) {
    console.log('\n🎉 جميع الأنظمة تعمل بنجاح!');
    console.log('يمكنك الآن استخدام نظام الأخبار في موقعك.');
  } else {
    console.log('\n⚠️ هناك مشاكل في بعض الأنظمة.');
    console.log('يرجى مراجعة الأخطاء أعلاه وإصلاحها.');
  }
  
  return allWorking;
}

// تشغيل الاختبارات
if (require.main === module) {
  // إضافة fetch للـ Node.js إذا لم تكن متوفرة
  if (typeof fetch === 'undefined') {
    global.fetch = require('node-fetch');
  }
  
  runAllTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ خطأ في تشغيل الاختبارات:', error);
      process.exit(1);
    });
}

module.exports = { testFirebaseFunction, testNewsAPI, testInternetConnection, runAllTests };
