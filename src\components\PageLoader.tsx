import React, { useState, useEffect } from 'react';
import { Loader2, Wifi, WifiOff } from 'lucide-react';

interface PageLoaderProps {
  message?: string;
  showProgress?: boolean;
}

const PageLoader: React.FC<PageLoaderProps> = ({ 
  message = "جاري التحميل...", 
  showProgress = false 
}) => {
  const [progress, setProgress] = useState(0);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [loadingTime, setLoadingTime] = useState(0);

  useEffect(() => {
    // مراقبة حالة الاتصال
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  useEffect(() => {
    if (!showProgress) return;

    // محاكاة شريط التقدم
    const interval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 90) return prev;
        return prev + Math.random() * 10;
      });
    }, 200);

    return () => clearInterval(interval);
  }, [showProgress]);

  useEffect(() => {
    // حساب وقت التحميل
    const timer = setInterval(() => {
      setLoadingTime(prev => prev + 1);
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black flex items-center justify-center">
      <div className="text-center max-w-md mx-auto px-4">
        {/* أيقونة التحميل */}
        <div className="mb-8">
          <div className="relative">
            <Loader2 className="w-16 h-16 text-amber-400 animate-spin mx-auto" />
            <div className="absolute inset-0 w-16 h-16 border-4 border-amber-400/20 rounded-full mx-auto animate-pulse"></div>
          </div>
        </div>

        {/* رسالة التحميل */}
        <h2 className="text-2xl font-bold text-amber-400 mb-4">
          {message}
        </h2>

        {/* شريط التقدم */}
        {showProgress && (
          <div className="mb-6">
            <div className="w-full bg-gray-700 rounded-full h-2 mb-2">
              <div 
                className="bg-amber-400 h-2 rounded-full transition-all duration-300 ease-out"
                style={{ width: `${progress}%` }}
              ></div>
            </div>
            <p className="text-sm text-gray-400">
              {Math.round(progress)}% مكتمل
            </p>
          </div>
        )}

        {/* حالة الاتصال */}
        <div className="flex items-center justify-center mb-4">
          {isOnline ? (
            <div className="flex items-center text-green-400">
              <Wifi className="w-4 h-4 ml-2" />
              <span className="text-sm">متصل بالإنترنت</span>
            </div>
          ) : (
            <div className="flex items-center text-red-400">
              <WifiOff className="w-4 h-4 ml-2" />
              <span className="text-sm">غير متصل بالإنترنت</span>
            </div>
          )}
        </div>

        {/* وقت التحميل */}
        <p className="text-xs text-gray-500">
          وقت التحميل: {loadingTime} ثانية
        </p>

        {/* رسالة تحذيرية للتحميل الطويل */}
        {loadingTime > 10 && (
          <div className="mt-4 p-3 bg-yellow-900/20 border border-yellow-500/30 rounded-lg">
            <p className="text-yellow-400 text-sm">
              ⚠️ التحميل يستغرق وقتاً أطول من المعتاد
            </p>
            <p className="text-gray-400 text-xs mt-1">
              يرجى التحقق من اتصال الإنترنت
            </p>
          </div>
        )}

        {/* رسالة عدم الاتصال */}
        {!isOnline && (
          <div className="mt-4 p-3 bg-red-900/20 border border-red-500/30 rounded-lg">
            <p className="text-red-400 text-sm">
              🔌 لا يوجد اتصال بالإنترنت
            </p>
            <p className="text-gray-400 text-xs mt-1">
              يرجى التحقق من اتصالك والمحاولة مرة أخرى
            </p>
          </div>
        )}

        {/* نقاط متحركة */}
        <div className="flex justify-center space-x-1 mt-6">
          {[0, 1, 2].map((i) => (
            <div
              key={i}
              className="w-2 h-2 bg-amber-400 rounded-full animate-bounce"
              style={{
                animationDelay: `${i * 0.2}s`,
                animationDuration: '1s'
              }}
            ></div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default PageLoader;
