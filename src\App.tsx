
import React, { Suspense } from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { ThemeProvider } from "@/components/ThemeProvider";
import Analytics from "@/components/Analytics";
import PerformanceMonitor from "@/components/PerformanceMonitor";
import ErrorBoundary from "@/components/ErrorBoundary";
import RouteGuard from "@/components/RouteGuard";
import PageLoader from "@/components/PageLoader";

// Lazy loading للصفحات لتحسين الأداء
const Index = React.lazy(() => import("./pages/Index"));
const About = React.lazy(() => import("./pages/About"));
const Projects = React.lazy(() => import("./pages/Projects"));
const Contact = React.lazy(() => import("./pages/Contact"));
const TechNews = React.lazy(() => import("./pages/TechNews"));
const GroupChat = React.lazy(() => import("./pages/GroupChat"));

const PDFTest = React.lazy(() => import("./pages/PDFTest"));
const NotFound = React.lazy(() => import("./pages/NotFound"));

// مكون Loading محسن
const LoadingSpinner = () => (
  <PageLoader
    message="جاري تحميل الصفحة..."
    showProgress={true}
  />
);

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 دقائق
      gcTime: 10 * 60 * 1000, // 10 دقائق (تم تغيير cacheTime إلى gcTime)
      retry: 3,
      refetchOnWindowFocus: false,
    },
  },
});

const App = () => (
  <ErrorBoundary>
    <QueryClientProvider client={queryClient}>
      <ThemeProvider defaultTheme="dark" storageKey="hodifa-theme">
        <TooltipProvider>
          <Analytics trackingId={import.meta.env.VITE_GA_TRACKING_ID} />
          <PerformanceMonitor />
          <Toaster />
          <Sonner />
          <BrowserRouter>
            <RouteGuard>
              <Suspense fallback={<LoadingSpinner />}>
                <Routes>
                  <Route path="/" element={<Index />} />

                  {/* About routes - multiple variations */}
                  <Route path="/about" element={<About />} />
                  <Route path="/About" element={<About />} />

                  {/* Projects routes - multiple variations */}
                  <Route path="/projects" element={<Projects />} />
                  <Route path="/Projects" element={<Projects />} />

                  {/* Contact routes - multiple variations */}
                  <Route path="/contact" element={<Contact />} />
                  <Route path="/Contact" element={<Contact />} />

                  {/* TechNews routes - multiple variations */}
                  <Route path="/tech-news" element={<TechNews />} />
                  <Route path="/TechNews" element={<TechNews />} />
                  <Route path="/technews" element={<TechNews />} />
                  <Route path="/tech_news" element={<TechNews />} />

                  {/* GroupChat routes - multiple variations */}
                  <Route path="/group-chat" element={<GroupChat />} />
                  <Route path="/GroupChat" element={<GroupChat />} />
                  <Route path="/groupchat" element={<GroupChat />} />
                  <Route path="/chat" element={<GroupChat />} />
                  <Route path="/Chat" element={<GroupChat />} />

                  {/* PDF Test route - for development and testing */}
                  <Route path="/pdf-test" element={<PDFTest />} />
                  <Route path="/PDFTest" element={<PDFTest />} />

                  {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
                  <Route path="*" element={<NotFound />} />
                </Routes>
              </Suspense>
            </RouteGuard>
          </BrowserRouter>
          {/* React Query DevTools - فقط في بيئة التطوير */}
          {import.meta.env.DEV && <ReactQueryDevtools initialIsOpen={false} />}
        </TooltipProvider>
      </ThemeProvider>
    </QueryClientProvider>
  </ErrorBoundary>
);

export default App;
