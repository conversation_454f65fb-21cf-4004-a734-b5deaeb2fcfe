#!/usr/bin/env node

/**
 * سكريبت نشر Firebase Functions
 * يقوم بنشر الدوال السحابية مع إعداد المتغيرات السرية
 */

const { execSync } = require('child_process');
const path = require('path');

console.log('🚀 بدء نشر Firebase Functions...');

try {
  // التأكد من وجود Firebase CLI
  try {
    execSync('firebase --version', { stdio: 'pipe' });
  } catch (error) {
    console.error('❌ Firebase CLI غير مثبت. يرجى تثبيته أولاً:');
    console.error('npm install -g firebase-tools');
    process.exit(1);
  }

  // الانتقال إلى مجلد functions
  const functionsDir = path.join(__dirname, '..', 'functions');
  process.chdir(functionsDir);

  console.log('📦 تثبيت التبعيات...');
  execSync('npm install', { stdio: 'inherit' });

  console.log('🔨 بناء المشروع...');
  execSync('npm run build', { stdio: 'inherit' });

  // العودة إلى المجلد الرئيسي
  process.chdir(path.join(__dirname, '..'));

  console.log('🔐 إعداد المتغيرات السرية...');
  
  // إعداد مفتاح NewsAPI كمتغير سري
  try {
    execSync('firebase functions:secrets:set NEWSAPI_KEY', { 
      stdio: 'inherit',
      input: '1660ff496c4247c3a7d49457501feb73\n'
    });
  } catch (error) {
    console.warn('⚠️ تعذر إعداد المتغير السري. سيتم المتابعة...');
  }

  console.log('🚀 نشر Functions...');
  execSync('firebase deploy --only functions', { stdio: 'inherit' });

  console.log('✅ تم نشر Firebase Functions بنجاح!');
  console.log('🌐 يمكنك الآن الوصول إلى الدوال عبر:');
  console.log('https://gettechnews-myprofilewebsitechatproject.cloudfunctions.net/getTechNews');

} catch (error) {
  console.error('❌ خطأ في نشر Functions:', error.message);
  process.exit(1);
}
