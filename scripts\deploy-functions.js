#!/usr/bin/env node

/**
 * سكريبت نشر Firebase Functions للأخبار التقنية العربية
 * يقوم بنشر الدوال السحابية مع إعداد المتغيرات السرية
 */

import { execSync } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🚀 بدء نشر Firebase Functions للأخبار التقنية العربية...');

try {
  // التأكد من وجود Firebase CLI
  try {
    execSync('firebase --version', { stdio: 'pipe' });
    console.log('✅ Firebase CLI متوفر');
  } catch (error) {
    console.error('❌ Firebase CLI غير مثبت. يرجى تثبيته أولاً:');
    console.error('npm install -g firebase-tools');
    process.exit(1);
  }

  // التحقق من تسجيل الدخول
  try {
    execSync('firebase projects:list', { stdio: 'pipe' });
    console.log('✅ تم تسجيل الدخول إلى Firebase');
  } catch (error) {
    console.log('🔐 يرجى تسجيل الدخول إلى Firebase...');
    execSync('firebase login', { stdio: 'inherit' });
  }

  // الانتقال إلى مجلد functions
  const functionsDir = path.join(__dirname, '..', 'functions');
  process.chdir(functionsDir);

  console.log('📦 تثبيت التبعيات...');
  execSync('npm install', { stdio: 'inherit' });

  console.log('🔨 بناء المشروع...');
  execSync('npm run build', { stdio: 'inherit' });

  // العودة إلى المجلد الرئيسي
  process.chdir(path.join(__dirname, '..'));

  console.log('🔐 إعداد المتغيرات السرية...');

  // إعداد مفتاح NewsAPI كمتغير سري
  try {
    console.log('📝 إعداد مفتاح NewsAPI...');
    // استخدام مفتاح API حقيقي للأخبار العربية
    const apiKey = process.env.NEWSAPI_KEY || '********************************';
    execSync(`echo "${apiKey}" | firebase functions:secrets:set NEWSAPI_KEY`, {
      stdio: 'inherit',
      shell: true
    });
    console.log('✅ تم إعداد مفتاح NewsAPI بنجاح');
  } catch (error) {
    console.warn('⚠️ تعذر إعداد المتغير السري. سيتم استخدام المفتاح الافتراضي...');
  }

  console.log('🚀 نشر Functions...');
  execSync('firebase deploy --only functions', { stdio: 'inherit' });

  console.log('✅ تم نشر Firebase Functions بنجاح!');
  console.log('🌐 يمكنك الآن الوصول إلى الدوال عبر:');
  console.log('https://gettechnews-myprofilewebsitechatproject.cloudfunctions.net/getTechNews');
  console.log('');
  console.log('📰 الميزات المتاحة:');
  console.log('- أخبار تقنية باللغة العربية');
  console.log('- كلمات مفتاحية: تقنية، تكنولوجيا، ذكاء اصطناعي');
  console.log('- حماية مفتاح API في السحابة');
  console.log('- أخبار احتياطية عالية الجودة');

} catch (error) {
  console.error('❌ خطأ في نشر Functions:', error.message);
  process.exit(1);
}
