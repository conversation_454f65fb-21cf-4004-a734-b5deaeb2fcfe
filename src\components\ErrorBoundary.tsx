import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Alert<PERSON>riangle, RefreshCw, Home } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    // تحديث الحالة لإظهار واجهة الخطأ
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // تسجيل الخطأ للمراقبة
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo
    });

    // إرسال الخطأ لخدمة المراقبة (اختياري)
    if (import.meta.env.PROD) {
      // يمكن إضافة خدمة مراقبة مثل Sentry هنا
      console.error('Production error:', {
        error: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack
      });
    }
  }

  handleReload = () => {
    // إعادة تحميل الصفحة
    window.location.reload();
  };

  handleGoHome = () => {
    // الذهاب للصفحة الرئيسية
    window.location.href = '/';
  };

  render() {
    if (this.state.hasError) {
      // يمكن عرض واجهة خطأ مخصصة
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black text-white flex items-center justify-center">
          <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            {/* خطأ في التطبيق */}
            <div className="mb-8">
              <AlertTriangle className="w-20 h-20 text-red-400 mx-auto mb-6 animate-pulse" />
              <h1 className="text-4xl lg:text-5xl font-bold text-red-400 mb-4">
                خطأ في التطبيق
              </h1>
              <div className="w-32 h-1 bg-red-400 mx-auto mb-8 rounded-full"></div>
            </div>

            {/* رسالة الخطأ */}
            <div className="mb-8">
              <h2 className="text-2xl lg:text-3xl font-bold text-white mb-4">
                عذراً، حدث خطأ غير متوقع
              </h2>
              <p className="text-xl text-gray-300 mb-4">
                نعتذر عن هذا الإزعاج. يرجى المحاولة مرة أخرى.
              </p>
              
              {/* تفاصيل الخطأ في بيئة التطوير */}
              {import.meta.env.DEV && this.state.error && (
                <div className="bg-gray-800/50 border border-red-500/30 rounded-lg p-4 mt-4 text-left">
                  <h3 className="text-red-400 font-semibold mb-2">تفاصيل الخطأ (بيئة التطوير):</h3>
                  <pre className="text-sm text-gray-300 overflow-auto">
                    {this.state.error.message}
                  </pre>
                  {this.state.error.stack && (
                    <details className="mt-2">
                      <summary className="text-red-400 cursor-pointer">Stack Trace</summary>
                      <pre className="text-xs text-gray-400 mt-2 overflow-auto">
                        {this.state.error.stack}
                      </pre>
                    </details>
                  )}
                </div>
              )}
            </div>

            {/* أزرار الإجراءات */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                onClick={this.handleReload}
                className="bg-red-500 hover:bg-red-600 text-white font-semibold px-8 py-3 rounded-full"
              >
                <RefreshCw className="w-4 h-4 ml-2" />
                إعادة تحميل الصفحة
              </Button>

              <Button
                onClick={this.handleGoHome}
                variant="outline"
                className="border-red-500/40 text-red-400 hover:bg-red-500/10 px-8 py-3 rounded-full"
              >
                <Home className="w-4 h-4 ml-2" />
                العودة للرئيسية
              </Button>
            </div>

            {/* معلومات إضافية */}
            <div className="mt-12 pt-8 border-t border-gray-700">
              <p className="text-gray-400 text-sm">
                إذا استمر هذا الخطأ، يرجى التواصل معنا عبر صفحة الاتصال
              </p>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
