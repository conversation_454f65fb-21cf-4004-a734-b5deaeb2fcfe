/**
 * مكون إدخال الرسائل في الدردشة
 */

import React, { useState, useRef, KeyboardEvent } from 'react';
import { Send, Smile, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface ChatInputProps {
  onSendMessage: (message: string) => Promise<boolean>;
  disabled?: boolean;
  placeholder?: string;
  maxLength?: number;
}

const ChatInput: React.FC<ChatInputProps> = ({
  onSendMessage,
  disabled = false,
  placeholder = "اكتب رسالتك هنا...",
  maxLength = 500
}) => {
  const [message, setMessage] = useState('');
  const [isSending, setIsSending] = useState(false);
  const inputRef = useRef<HTMLTextAreaElement>(null);

  // إرسال الرسالة
  const handleSendMessage = async () => {
    const trimmedMessage = message.trim();
    
    if (!trimmedMessage || isSending || disabled) {
      return;
    }

    setIsSending(true);
    
    try {
      const success = await onSendMessage(trimmedMessage);
      
      if (success) {
        setMessage('');
        // إعادة التركيز على حقل الإدخال
        if (inputRef.current) {
          inputRef.current.focus();
        }
      }
    } catch (error) {
      console.error('خطأ في إرسال الرسالة:', error);
    } finally {
      setIsSending(false);
    }
  };

  // معالجة الضغط على Enter
  const handleKeyPress = (e: KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // تعديل ارتفاع textarea تلقائياً
  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    
    // تحديد طول الرسالة
    if (value.length <= maxLength) {
      setMessage(value);
    }

    // تعديل الارتفاع
    const textarea = e.target;
    textarea.style.height = 'auto';
    textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
  };

  const remainingChars = maxLength - message.length;
  const isNearLimit = remainingChars <= 50;

  return (
    <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/50">
      {/* حقل الإدخال */}
      <div className="relative">
        <textarea
          ref={inputRef}
          value={message}
          onChange={handleInputChange}
          onKeyPress={handleKeyPress}
          placeholder={placeholder}
          disabled={disabled || isSending}
          className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg px-4 py-3 pr-12 text-white placeholder-gray-400 resize-none focus:outline-none focus:ring-2 focus:ring-amber-500/50 focus:border-amber-500/50 transition-all duration-200"
          style={{ minHeight: '44px', maxHeight: '120px' }}
          rows={1}
        />

        {/* أيقونة الإيموجي (يمكن تطويرها لاحقاً) */}
        <button
          type="button"
          className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-amber-400 transition-colors"
          disabled={disabled || isSending}
        >
          <Smile className="w-5 h-5" />
        </button>
      </div>

      {/* شريط الأدوات */}
      <div className="flex items-center justify-between mt-3">
        {/* عداد الأحرف */}
        <div className="flex items-center gap-2 text-xs">
          <span className={`${isNearLimit ? 'text-amber-400' : 'text-gray-500'}`}>
            {remainingChars} حرف متبقي
          </span>
          
          {message.trim() && (
            <span className="text-gray-500">
              • اضغط Enter للإرسال
            </span>
          )}
        </div>

        {/* زر الإرسال */}
        <Button
          onClick={handleSendMessage}
          disabled={!message.trim() || disabled || isSending}
          className="bg-amber-500 hover:bg-amber-600 text-black font-semibold px-6 py-2 rounded-lg transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
        >
          {isSending ? (
            <>
              <Loader2 className="w-4 h-4 ml-2 animate-spin" />
              جاري الإرسال...
            </>
          ) : (
            <>
              <Send className="w-4 h-4 ml-2" />
              إرسال
            </>
          )}
        </Button>
      </div>

      {/* رسائل المساعدة */}
      {disabled && (
        <div className="mt-2 text-xs text-red-400 bg-red-900/20 border border-red-500/30 rounded px-3 py-2">
          ⚠️ يرجى تعيين اسم المستخدم أولاً
        </div>
      )}
    </div>
  );
};

export default ChatInput;
