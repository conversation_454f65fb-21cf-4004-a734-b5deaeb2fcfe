# 🚀 تعليمات نشر نظام الأخبار التقنية

## 📋 الخطوات المطلوبة

### 1. إعداد Firebase Functions

```bash
# الانتقال إلى مجلد functions
cd functions

# تثبيت التبعيات
npm install

# بناء المشروع
npm run build
```

### 2. إعد<PERSON> المتغيرات السرية

```bash
# تسجيل الدخول إلى Firebase
firebase login

# إعداد مفتاح NewsAPI كمتغير سري
firebase functions:secrets:set NEWSAPI_KEY
# أدخل: ********************************
```

### 3. نشر Firebase Functions

```bash
# العودة إلى المجلد الرئيسي
cd ..

# نشر Functions
firebase deploy --only functions

# أو استخدام السكريبت المخصص
npm run deploy:functions
```

### 4. التحقق من عمل النظام

```bash
# تشغيل اختبار النظام
node scripts/test-news-api.js
```

### 5. تحديث URL في React App

بعد النشر، ستحصل على URL مثل:
```
https://gettechnews-myprofilewebsitechatproject.cloudfunctions.net/getTechNews
```

تأكد من أن هذا URL محدث في `src/pages/TechNews.tsx`

### 6. نشر React App

```bash
# بناء المشروع
npm run build:production

# نشر على Firebase Hosting
npm run deploy:firebase
```

## 🔍 استكشاف الأخطاء

### مشكلة: Function لا تعمل
```bash
# فحص logs
firebase functions:log

# إعادة نشر
firebase deploy --only functions --force
```

### مشكلة: مفتاح API لا يعمل
```bash
# حذف المتغير السري
firebase functions:secrets:delete NEWSAPI_KEY

# إعادة إعداده
firebase functions:secrets:set NEWSAPI_KEY
```

### مشكلة: CORS
- تأكد من إضافة الدومين في `corsHandler` في `functions/src/index.ts`
- أعد نشر Functions بعد التعديل

## ✅ التحقق من النجاح

1. **Firebase Function**: يجب أن تعمل على URL المحدد
2. **React App**: يجب أن تعرض الأخبار في `/tech-news`
3. **Console Logs**: يجب أن تظهر رسائل نجاح في المتصفح

## 📞 الدعم

إذا واجهت أي مشاكل، راجع:
- `TECH_NEWS_SYSTEM_GUIDE.md` للتفاصيل الفنية
- Firebase Console للـ logs
- Browser DevTools للأخطاء في Frontend
