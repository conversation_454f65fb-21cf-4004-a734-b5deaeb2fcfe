import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Loader2 } from 'lucide-react';

interface RouteGuardProps {
  children: React.ReactNode;
}

// قائمة المسارات الصحيحة
const validRoutes = [
  '/',
  '/about',
  '/About',
  '/projects',
  '/Projects',
  '/contact',
  '/Contact',
  '/tech-news',
  '/TechNews',
  '/technews',
  '/tech_news',
  '/group-chat',
  '/GroupChat',
  '/groupchat',
  '/chat',
  '/Chat',
  '/pdf-test',
  '/PDFTest'
];

// قائمة المسارات المحذوفة أو القديمة
const deprecatedRoutes: { [key: string]: string } = {
  '/old-about': '/about',
  '/old-projects': '/projects',
  '/old-contact': '/contact',
  '/news': '/tech-news',
  '/blog': '/tech-news',
  '/articles': '/tech-news',
  '/portfolio': '/projects',
  '/work': '/projects',
  '/cv': '/about',
  '/resume': '/about',
  '/bio': '/about',
  '/profile': '/about'
};

const RouteGuard: React.FC<RouteGuardProps> = ({ children }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const [isValidating, setIsValidating] = useState(true);

  useEffect(() => {
    const validateRoute = () => {
      const currentPath = location.pathname;
      
      console.log('🔍 RouteGuard: Validating route:', currentPath);

      // التحقق من المسارات الصحيحة
      if (validRoutes.includes(currentPath)) {
        console.log('✅ RouteGuard: Valid route confirmed');
        setIsValidating(false);
        return;
      }

      // التحقق من المسارات المحذوفة والتوجيه للبديل
      if (deprecatedRoutes[currentPath]) {
        const redirectTo = deprecatedRoutes[currentPath];
        console.log(`🔄 RouteGuard: Redirecting deprecated route ${currentPath} to ${redirectTo}`);
        navigate(redirectTo, { replace: true });
        return;
      }

      // التحقق من المسارات المشابهة (case-insensitive)
      const lowerPath = currentPath.toLowerCase();
      const matchingRoute = validRoutes.find(route => 
        route.toLowerCase() === lowerPath
      );

      if (matchingRoute && matchingRoute !== currentPath) {
        console.log(`🔄 RouteGuard: Redirecting case mismatch ${currentPath} to ${matchingRoute}`);
        navigate(matchingRoute, { replace: true });
        return;
      }

      // التحقق من المسارات مع slash إضافي
      const trimmedPath = currentPath.replace(/\/+$/, '') || '/';
      if (trimmedPath !== currentPath && validRoutes.includes(trimmedPath)) {
        console.log(`🔄 RouteGuard: Redirecting trailing slash ${currentPath} to ${trimmedPath}`);
        navigate(trimmedPath, { replace: true });
        return;
      }

      // التحقق من المسارات مع معاملات query
      const pathWithoutQuery = currentPath.split('?')[0];
      if (pathWithoutQuery !== currentPath && validRoutes.includes(pathWithoutQuery)) {
        console.log(`🔄 RouteGuard: Redirecting with query params ${currentPath} to ${pathWithoutQuery}`);
        navigate(pathWithoutQuery, { replace: true });
        return;
      }

      // إذا لم يتم العثور على مسار صحيح، اتركه للـ NotFound component
      console.log('❌ RouteGuard: Invalid route, will show 404');
      setIsValidating(false);
    };

    // تأخير قصير للتأكد من تحميل React Router بالكامل
    const timer = setTimeout(validateRoute, 100);
    
    return () => clearTimeout(timer);
  }, [location.pathname, navigate]);

  // عرض مؤشر التحميل أثناء التحقق
  if (isValidating) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 text-amber-400 animate-spin mx-auto mb-4" />
          <p className="text-amber-400 text-lg">جاري التحقق من المسار...</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
};

export default RouteGuard;
