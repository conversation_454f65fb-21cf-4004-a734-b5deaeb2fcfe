/**
 * Hook مخصص لإدارة الدردشة الحية
 * يتعامل مع جميع عمليات الدردشة والاشتراكات
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { chatService, chatUtils, ChatMessage } from '@/lib/supabase';

interface UseChatOptions {
  maxMessages?: number;
  autoScroll?: boolean;
}

interface UseChatReturn {
  // حالة الدردشة
  messages: ChatMessage[];
  isLoading: boolean;
  isConnected: boolean;
  error: string | null;
  
  // معلومات المستخدم
  username: string;
  userColor: string;
  isUsernameSet: boolean;
  
  // إدارة الرسائل
  sendMessage: (message: string) => Promise<boolean>;
  setUsername: (name: string) => void;
  clearMessages: () => void;
  
  // إحصائيات
  messageCount: number;
  onlineUsers: number;
}

export const useChat = (options: UseChatOptions = {}): UseChatReturn => {
  const { maxMessages = 100, autoScroll = true } = options;
  
  // حالة الدردشة
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // معلومات المستخدم
  const [username, setUsernameState] = useState('');
  const [userColor, setUserColor] = useState('');
  const [isUsernameSet, setIsUsernameSet] = useState(false);
  
  // إحصائيات
  const [onlineUsers, setOnlineUsers] = useState(0);
  
  // مراجع للاشتراكات
  const subscriptionRef = useRef<any>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // تهيئة المستخدم
  useEffect(() => {
    const savedUsername = localStorage.getItem('chat_username');
    const savedColor = localStorage.getItem('chat_user_color');
    
    if (savedUsername && chatUtils.isValidUsername(savedUsername)) {
      setUsernameState(savedUsername);
      setIsUsernameSet(true);
    } else {
      const randomUsername = chatUtils.getRandomUsername();
      setUsernameState(randomUsername);
    }
    
    if (savedColor) {
      setUserColor(savedColor);
    } else {
      const randomColor = chatUtils.getRandomColor();
      setUserColor(randomColor);
      localStorage.setItem('chat_user_color', randomColor);
    }
  }, []);

  // تحميل الرسائل الأولية
  const loadInitialMessages = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const recentMessages = await chatService.getRecentMessages(maxMessages);
      setMessages(recentMessages);
      setIsConnected(true);
      
      console.log(`✅ تم تحميل ${recentMessages.length} رسالة`);
    } catch (err) {
      console.error('خطأ في تحميل الرسائل:', err);
      setError('فشل في تحميل الرسائل');
      setIsConnected(false);
    } finally {
      setIsLoading(false);
    }
  }, [maxMessages]);

  // الاشتراك في الرسائل الجديدة
  const subscribeToNewMessages = useCallback(() => {
    try {
      console.log('🔄 بدء الاشتراك في الرسائل الجديدة...');
      
      subscriptionRef.current = chatService.subscribeToMessages((newMessage: ChatMessage) => {
        console.log('📨 رسالة جديدة:', newMessage);
        
        setMessages(prevMessages => {
          // تجنب الرسائل المكررة
          const exists = prevMessages.some(msg => msg.id === newMessage.id);
          if (exists) return prevMessages;
          
          // إضافة الرسالة الجديدة والحفاظ على الحد الأقصى
          const updatedMessages = [...prevMessages, newMessage];
          return updatedMessages.slice(-maxMessages);
        });
        
        // تحديث عداد المستخدمين المتصلين (تقدير)
        setOnlineUsers(prev => Math.max(prev, Math.floor(Math.random() * 10) + 1));
      });
      
      setIsConnected(true);
      console.log('✅ تم الاشتراك بنجاح');
    } catch (err) {
      console.error('خطأ في الاشتراك:', err);
      setError('فشل في الاتصال بالخادم');
      setIsConnected(false);
    }
  }, [maxMessages]);

  // تهيئة الدردشة
  useEffect(() => {
    loadInitialMessages();
    subscribeToNewMessages();
    
    // تنظيف الاشتراكات عند إلغاء التحميل
    return () => {
      if (subscriptionRef.current) {
        chatService.unsubscribe(subscriptionRef.current);
        console.log('🔌 تم إلغاء الاشتراك');
      }
    };
  }, [loadInitialMessages, subscribeToNewMessages]);

  // التمرير التلقائي للأسفل
  useEffect(() => {
    if (autoScroll && messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages, autoScroll]);

  // تحديث حالة المستخدم
  useEffect(() => {
    if (isUsernameSet && username) {
      chatService.updateUserStatus(username, true);
      
      // تحديث الحالة عند إغلاق الصفحة
      const handleBeforeUnload = () => {
        chatService.updateUserStatus(username, false);
      };
      
      window.addEventListener('beforeunload', handleBeforeUnload);
      return () => window.removeEventListener('beforeunload', handleBeforeUnload);
    }
  }, [username, isUsernameSet]);

  // إرسال رسالة
  const sendMessage = useCallback(async (message: string): Promise<boolean> => {
    if (!message.trim() || !username || !isConnected) {
      return false;
    }

    try {
      const sanitizedMessage = chatUtils.sanitizeMessage(message);
      if (!sanitizedMessage) return false;

      const success = await chatService.sendMessage(username, sanitizedMessage, userColor);
      
      if (success) {
        console.log('✅ تم إرسال الرسالة بنجاح');
        return true;
      } else {
        setError('فشل في إرسال الرسالة');
        return false;
      }
    } catch (err) {
      console.error('خطأ في إرسال الرسالة:', err);
      setError('خطأ في إرسال الرسالة');
      return false;
    }
  }, [username, userColor, isConnected]);

  // تعيين اسم المستخدم
  const setUsername = useCallback((name: string) => {
    const trimmedName = name.trim();
    
    if (chatUtils.isValidUsername(trimmedName)) {
      setUsernameState(trimmedName);
      setIsUsernameSet(true);
      localStorage.setItem('chat_username', trimmedName);
      setError(null);
      
      // تحديث حالة المستخدم
      chatService.updateUserStatus(trimmedName, true);
    } else {
      setError('اسم المستخدم غير صحيح (2-20 حرف، أحرف وأرقام فقط)');
    }
  }, []);

  // مسح الرسائل (محلياً فقط)
  const clearMessages = useCallback(() => {
    setMessages([]);
  }, []);

  // إعادة الاتصال
  const reconnect = useCallback(() => {
    setError(null);
    loadInitialMessages();
    subscribeToNewMessages();
  }, [loadInitialMessages, subscribeToNewMessages]);

  // مراقبة حالة الاتصال
  useEffect(() => {
    const checkConnection = () => {
      if (!navigator.onLine) {
        setIsConnected(false);
        setError('لا يوجد اتصال بالإنترنت');
      } else if (error && !isConnected) {
        reconnect();
      }
    };

    window.addEventListener('online', checkConnection);
    window.addEventListener('offline', checkConnection);
    
    return () => {
      window.removeEventListener('online', checkConnection);
      window.removeEventListener('offline', checkConnection);
    };
  }, [error, isConnected, reconnect]);

  return {
    // حالة الدردشة
    messages,
    isLoading,
    isConnected,
    error,
    
    // معلومات المستخدم
    username,
    userColor,
    isUsernameSet,
    
    // إدارة الرسائل
    sendMessage,
    setUsername,
    clearMessages,
    
    // إحصائيات
    messageCount: messages.length,
    onlineUsers,
  };
};
