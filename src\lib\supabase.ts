/**
 * إعداد Supabase للدردشة الحية
 * مجاني 100% - لا يتطلب بطاقة ائتمان
 */

import { createClient } from '@supabase/supabase-js';

// إعدادات Supabase (يجب إضافتها في متغيرات البيئة)
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://your-project.supabase.co';
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'your-anon-key';

// إنشاء عميل Supabase
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  realtime: {
    params: {
      eventsPerSecond: 10, // تحديد عدد الأحداث في الثانية
    },
  },
});

// أنواع البيانات للدردشة
export interface ChatMessage {
  id: string;
  username: string;
  message: string;
  created_at: string;
  user_color?: string;
}

export interface ChatUser {
  username: string;
  color: string;
  isOnline: boolean;
  lastSeen: string;
}

// دوال مساعدة للدردشة
export const chatService = {
  // جلب الرسائل الأخيرة
  async getRecentMessages(limit: number = 50): Promise<ChatMessage[]> {
    try {
      const { data, error } = await supabase
        .from('chat_messages')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Error fetching messages:', error);
        return [];
      }

      return (data || []).reverse(); // ترتيب من الأقدم للأحدث
    } catch (error) {
      console.error('Error in getRecentMessages:', error);
      return [];
    }
  },

  // إرسال رسالة جديدة
  async sendMessage(username: string, message: string, userColor: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('chat_messages')
        .insert([
          {
            username: username.trim(),
            message: message.trim(),
            user_color: userColor,
            created_at: new Date().toISOString(),
          },
        ]);

      if (error) {
        console.error('Error sending message:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in sendMessage:', error);
      return false;
    }
  },

  // الاشتراك في الرسائل الجديدة
  subscribeToMessages(callback: (message: ChatMessage) => void) {
    const subscription = supabase
      .channel('chat_messages')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'chat_messages',
        },
        (payload) => {
          console.log('New message received:', payload);
          if (payload.new) {
            callback(payload.new as ChatMessage);
          }
        }
      )
      .subscribe();

    return subscription;
  },

  // إلغاء الاشتراك
  unsubscribe(subscription: any) {
    if (subscription) {
      supabase.removeChannel(subscription);
    }
  },

  // تحديث حالة المستخدم (اختياري)
  async updateUserStatus(username: string, isOnline: boolean) {
    try {
      const { error } = await supabase
        .from('chat_users')
        .upsert([
          {
            username: username.trim(),
            is_online: isOnline,
            last_seen: new Date().toISOString(),
          },
        ]);

      if (error) {
        console.error('Error updating user status:', error);
      }
    } catch (error) {
      console.error('Error in updateUserStatus:', error);
    }
  },

  // جلب المستخدمين المتصلين
  async getOnlineUsers(): Promise<ChatUser[]> {
    try {
      const { data, error } = await supabase
        .from('chat_users')
        .select('*')
        .eq('is_online', true)
        .order('last_seen', { ascending: false });

      if (error) {
        console.error('Error fetching online users:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error in getOnlineUsers:', error);
      return [];
    }
  },
};

// دوال مساعدة للألوان والأسماء
export const chatUtils = {
  // ألوان عشوائية للمستخدمين
  getRandomColor(): string {
    const colors = [
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
      '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
      '#F8C471', '#82E0AA', '#F1948A', '#85C1E9', '#D7BDE2'
    ];
    return colors[Math.floor(Math.random() * colors.length)];
  },

  // أسماء عشوائية للزوار
  getRandomUsername(): string {
    const adjectives = [
      'سريع', 'ذكي', 'مبدع', 'نشيط', 'هادئ', 'مرح', 'لطيف', 'قوي',
      'جميل', 'رائع', 'مميز', 'فريد', 'عبقري', 'ماهر', 'بارع'
    ];
    
    const nouns = [
      'نمر', 'أسد', 'نسر', 'صقر', 'دولفين', 'فهد', 'ذئب', 'حصان',
      'طائر', 'سمكة', 'فراشة', 'نحلة', 'قطة', 'كلب', 'أرنب'
    ];

    const adjective = adjectives[Math.floor(Math.random() * adjectives.length)];
    const noun = nouns[Math.floor(Math.random() * nouns.length)];
    const number = Math.floor(Math.random() * 999) + 1;

    return `${adjective}_${noun}_${number}`;
  },

  // تنسيق التاريخ
  formatMessageTime(dateString: string): string {
    try {
      const date = new Date(dateString);
      const now = new Date();
      const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

      if (diffInMinutes < 1) {
        return 'الآن';
      } else if (diffInMinutes < 60) {
        return `منذ ${diffInMinutes} دقيقة`;
      } else if (diffInMinutes < 1440) { // أقل من 24 ساعة
        const hours = Math.floor(diffInMinutes / 60);
        return `منذ ${hours} ساعة`;
      } else {
        return date.toLocaleDateString('ar-SA', {
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        });
      }
    } catch (error) {
      return 'وقت غير معروف';
    }
  },

  // تنظيف النص
  sanitizeMessage(message: string): string {
    return message
      .trim()
      .replace(/\s+/g, ' ') // إزالة المسافات الزائدة
      .substring(0, 500); // تحديد طول الرسالة
  },

  // التحقق من صحة اسم المستخدم
  isValidUsername(username: string): boolean {
    const trimmed = username.trim();
    return trimmed.length >= 2 && trimmed.length <= 20 && /^[a-zA-Z0-9_\u0600-\u06FF\s]+$/.test(trimmed);
  },
};
