# 📰 دليل نظام الأخبار التقنية المجاني 100%

## 🎯 نظرة عامة

تم إنشاء نظام مجاني 100% لعرض الأخبار التقنية باللغة العربية والإنجليزية باستخدام:
- ✅ **Firebase Functions** (مجاني - Spark Plan)
- ✅ **NewsAPI.org** (مفتاح مجاني)
- ✅ **React + TypeScript + Tailwind CSS**
- ✅ **أخبار ثابتة احتياطية** عالية الجودة

## 🏗️ هيكل النظام

### 1. Firebase Function (`functions/src/index.ts`)
```typescript
export const getTechNews = onRequest({
  cors: true,
  maxInstances: 10,
  timeoutSeconds: 30,
  memory: "256MiB",
  secrets: [newsApiKey]
}, async (request, response) => {
  // جلب الأخبار من NewsAPI.org باللغتين العربية والإنجليزية
  // معالجة البيانات وتنسيقها
  // إرجاع أخبار ثابتة في حالة الفشل
});
```

### 2. React Component (`src/pages/TechNews.tsx`)
```typescript
const fetchTechNews = async () => {
  // استدعاء Firebase Function
  const functionUrl = 'https://gettechnews-myprofilewebsitechatproject.cloudfunctions.net/getTechNews';
  // معالجة الاستجابة
  // العودة للأخبار الثابتة في حالة الخطأ
};
```

## 🚀 خطوات التشغيل

### 1. إعداد Firebase Functions

```bash
# الانتقال إلى مجلد functions
cd functions

# تثبيت التبعيات
npm install

# بناء المشروع
npm run build
```

### 2. إعداد المتغيرات السرية

```bash
# إعداد مفتاح NewsAPI كمتغير سري
firebase functions:secrets:set NEWSAPI_KEY
# أدخل: ********************************
```

### 3. نشر Functions

```bash
# نشر Functions
firebase deploy --only functions

# أو استخدام السكريبت المخصص
npm run deploy:functions
```

### 4. تشغيل React App

```bash
# تشغيل التطوير
npm run dev

# بناء الإنتاج
npm run build:production
```

## 🔧 الميزات

### ✅ مصادر الأخبار
- **NewsAPI.org** - أخبار عربية وإنجليزية
- **أخبار ثابتة احتياطية** - محتوى عالي الجودة

### ✅ اللغات المدعومة
- **العربية**: تكنولوجيا، تقنية، ذكاء اصطناعي، برمجة
- **الإنجليزية**: technology, programming, AI, software

### ✅ الأمان
- مفتاح API محمي في Firebase Functions
- CORS محدود للدومينات المصرح بها
- معالجة شاملة للأخطاء

## 🌐 URLs المهمة

- **Firebase Function**: `https://gettechnews-myprofilewebsitechatproject.cloudfunctions.net/getTechNews`
- **صفحة الأخبار**: `/tech-news`
- **Firebase Console**: `https://console.firebase.google.com/project/myprofilewebsitechatproject`

## 📊 استجابة API

```json
{
  "success": true,
  "source": "NewsAPI.org - Arabic Tech, NewsAPI.org - English Tech",
  "articles": [
    {
      "article_id": "newsapi_org_arabic_tech_0",
      "title": "عنوان الخبر",
      "description": "وصف الخبر",
      "content": "محتوى الخبر",
      "link": "https://example.com",
      "image_url": "https://example.com/image.jpg",
      "source_id": "اسم المصدر",
      "category": ["technology"],
      "pubDate": "2025-01-15T10:30:00Z",
      "language": "ar"
    }
  ],
  "timestamp": "2025-01-15T12:00:00.000Z",
  "count": 12
}
```

## 🛠️ استكشاف الأخطاء

### مشكلة: Function لا تعمل
```bash
# فحص logs
firebase functions:log

# إعادة نشر
firebase deploy --only functions
```

### مشكلة: مفتاح API لا يعمل
```bash
# إعادة إعداد المتغير السري
firebase functions:secrets:delete NEWSAPI_KEY
firebase functions:secrets:set NEWSAPI_KEY
```

### مشكلة: CORS
- تأكد من إضافة الدومين في `corsHandler`
- تحقق من headers في الطلب

## 💰 التكلفة

- **Firebase Functions**: مجاني (Spark Plan)
- **NewsAPI.org**: مجاني (1000 طلب/شهر)
- **Firebase Hosting**: مجاني
- **إجمالي التكلفة**: **0 ريال سعودي** 🎉

## 🔄 التحديثات المستقبلية

1. إضافة مصادر أخبار إضافية
2. تحسين خوارزمية الفلترة
3. إضافة ميزة البحث
4. دعم المزيد من اللغات

---

**تم إنشاؤه بواسطة**: حذيفة عبدالمعز  
**التاريخ**: يناير 2025  
**الإصدار**: 1.0.0
