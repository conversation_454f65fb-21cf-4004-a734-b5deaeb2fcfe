/**
 * Vercel Edge Function للأخبار التقنية العربية
 * مجاني 100% - يدعم serverless functions مجانًا
 */

// مفتاح NewsAPI (يجب إضافته في Environment Variables)
const NEWS_API_KEY = process.env.NEWS_API_KEY || '1660ff496c4247c3a7d49457501feb73';

// دالة للحصول على أخبار ثابتة احتياطية
function getFallbackNews() {
  return {
    success: true,
    source: "Static Fallback - Arabic Tech News",
    articles: [
      {
        article_id: "static_ar_1",
        title: "الذكاء الاصطناعي يغير مستقبل التقنية في العالم العربي",
        description: "تشهد المنطقة العربية نمواً متسارعاً في تطبيقات الذكاء الاصطناعي، مع استثمارات ضخمة في التقنيات الحديثة والابتكار التكنولوجي.",
        content: "الدول العربية تستثمر بقوة في تقنيات الذكاء الاصطناعي لتطوير الخدمات الحكومية والقطاعات الاقتصادية المختلفة.",
        link: "https://www.ai-arabia.com",
        image_url: "https://images.unsplash.com/photo-1677442136019-21780ecad995?w=500&h=300&fit=crop",
        source_id: "تقنية عربية",
        category: ["ذكاء اصطناعي", "تقنية"],
        pubDate: "2025-01-15T10:30:00Z",
        language: "ar"
      },
      {
        article_id: "static_ar_2",
        title: "تطوير تطبيقات الهواتف الذكية باستخدام React Native في المنطقة العربية",
        description: "يشهد سوق تطوير التطبيقات في العالم العربي نمواً كبيراً، مع تزايد الطلب على تطبيقات الهواتف الذكية المطورة بتقنيات حديثة.",
        content: "المطورون العرب يتجهون بقوة نحو استخدام React Native لتطوير تطبيقات متعددة المنصات بكفاءة عالية وتكلفة أقل.",
        link: "https://www.react-native-arabia.com",
        image_url: "https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=500&h=300&fit=crop",
        source_id: "تطوير عربي",
        category: ["تطوير تطبيقات", "برمجة"],
        pubDate: "2025-01-14T09:15:00Z",
        language: "ar"
      },
      {
        article_id: "static_ar_3",
        title: "تقنيات البلوك تشين والعملات الرقمية في الشرق الأوسط",
        description: "تشهد منطقة الشرق الأوسط اهتماماً متزايداً بتقنيات البلوك تشين والعملات الرقمية، مع إطلاق مشاريع رائدة في هذا المجال.",
        content: "الإمارات والسعودية تقودان المنطقة في تبني تقنيات البلوك تشين لتطوير الخدمات الحكومية والمالية.",
        link: "https://www.blockchain-arabia.com",
        image_url: "https://images.unsplash.com/photo-1639762681485-074b7f938ba0?w=500&h=300&fit=crop",
        source_id: "بلوك تشين عربي",
        category: ["بلوك تشين", "تقنية مالية"],
        pubDate: "2025-01-13T09:15:00Z",
        language: "ar"
      },
      {
        article_id: "static_ar_4",
        title: "الأمن السيبراني في عصر الذكاء الاصطناعي والحوسبة الكمية",
        description: "مع ظهور تقنيات الذكاء الاصطناعي والحوسبة الكمية، تواجه الشركات تحديات أمنية جديدة تتطلب استراتيجيات حماية متطورة ومبتكرة.",
        content: "تقنيات الأمان الجديدة تستخدم الذكاء الاصطناعي لحماية البيانات من التهديدات السيبرانية المتطورة والهجمات الكمية المستقبلية.",
        link: "https://www.cybersecurity-arabia.com",
        image_url: "https://images.unsplash.com/photo-1563986768609-322da13575f3?w=500&h=300&fit=crop",
        source_id: "أمن المعلومات العربي",
        category: ["أمن المعلومات", "ذكاء اصطناعي"],
        pubDate: "2025-01-12T09:15:00Z",
        language: "ar"
      },
      {
        article_id: "static_en_1",
        title: "React 19 Brings Revolutionary Features for Modern UI Development",
        description: "The new React version introduces enhanced Server Components, a new Compiler, and advanced features that make it easier for developers to build high-performance web applications.",
        content: "React 19 comes with features like the new React Compiler, improvements in Concurrent Features, and enhanced development tools for a better developer experience.",
        link: "https://react.dev",
        image_url: "https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=500&h=300&fit=crop",
        source_id: "React Blog",
        category: ["web development", "React"],
        pubDate: "2025-01-11T14:20:00Z",
        language: "en"
      },
      {
        article_id: "static_en_2",
        title: "WebAssembly 2025: The Future of High-Performance Computing in Browsers",
        description: "WebAssembly evolves to support more languages and features, with new capabilities for parallel computing and AI in browsers.",
        content: "With WASI support and more languages, developers can now run complex applications with near-native performance directly in the browser.",
        link: "https://webassembly.org",
        image_url: "https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=500&h=300&fit=crop",
        source_id: "WebAssembly Foundation",
        category: ["web development", "WebAssembly"],
        pubDate: "2025-01-10T13:20:00Z",
        language: "en"
      }
    ],
    timestamp: new Date().toISOString(),
    count: 6
  };
}

export default async function handler(req, res) {
  // إعداد CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  // التعامل مع طلبات OPTIONS (CORS preflight)
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  // التأكد من أن الطلب GET
  if (req.method !== 'GET') {
    res.status(405).json({ error: 'Method not allowed' });
    return;
  }

  try {
    console.log('🔄 Fetching Arabic tech news from NewsAPI...');

    // مصادر الأخبار المختلفة - التركيز على الأخبار العربية
    const newsSources = [
      {
        name: "NewsAPI.org - Arabic Tech Primary",
        url: "https://newsapi.org/v2/everything",
        params: {
          q: "تقنية OR تكنولوجيا OR ذكاء اصطناعي",
          language: "ar",
          sortBy: "publishedAt",
          pageSize: "10",
          apiKey: NEWS_API_KEY
        }
      },
      {
        name: "NewsAPI.org - Arabic Tech Secondary",
        url: "https://newsapi.org/v2/everything",
        params: {
          q: "برمجة OR تطوير OR حاسوب OR إنترنت OR تطبيقات",
          language: "ar",
          sortBy: "publishedAt",
          pageSize: "6",
          apiKey: NEWS_API_KEY
        }
      },
      {
        name: "NewsAPI.org - English Tech Fallback",
        url: "https://newsapi.org/v2/everything",
        params: {
          q: "technology OR programming OR AI OR software",
          language: "en",
          sortBy: "publishedAt",
          pageSize: "4",
          apiKey: NEWS_API_KEY
        }
      }
    ];

    let allArticles = [];
    let successfulSources = [];

    // جرب كل مصدر
    for (const source of newsSources) {
      try {
        console.log(`🔄 Trying ${source.name}...`);
        
        const queryParams = new URLSearchParams(source.params).toString();
        const apiUrl = `${source.url}?${queryParams}`;

        const response = await fetch(apiUrl);
        
        if (response.ok) {
          const data = await response.json();
          
          if (data.articles && data.articles.length > 0) {
            const processedArticles = data.articles
              .filter(article => 
                article.title && 
                article.description && 
                article.url &&
                !article.title.toLowerCase().includes('[removed]') &&
                !article.description.toLowerCase().includes('[removed]')
              )
              .map((article, index) => ({
                article_id: `${source.name.toLowerCase().replace(/[^a-z0-9]/g, '_')}_${index}`,
                title: article.title,
                description: article.description,
                content: article.content,
                link: article.url,
                image_url: article.urlToImage,
                source_id: article.source?.name || source.name,
                category: ["technology"],
                pubDate: article.publishedAt,
                language: source.params.language
              }));

            if (processedArticles.length > 0) {
              allArticles = [...allArticles, ...processedArticles];
              successfulSources.push(source.name);
              console.log(`✅ ${source.name} returned ${processedArticles.length} articles`);
            }
          }
        } else {
          console.warn(`❌ ${source.name} API call failed with status: ${response.status}`);
        }
      } catch (sourceError) {
        console.warn(`❌ ${source.name} failed:`, sourceError);
        continue;
      }
    }

    // ترتيب المقالات حسب التاريخ وأخذ أحدث 12 مقال
    const filteredArticles = allArticles
      .sort((a, b) => new Date(b.pubDate).getTime() - new Date(a.pubDate).getTime())
      .slice(0, 12);

    // إذا فشلت جميع المصادر، استخدم أخبار ثابتة محدثة
    if (filteredArticles.length === 0) {
      console.log('📰 Using fallback static news');
      const fallbackData = getFallbackNews();
      res.status(200).json(fallbackData);
      return;
    }

    console.log(`✅ Successfully fetched ${filteredArticles.length} articles from ${successfulSources.join(', ')}`);

    const responseData = {
      success: true,
      source: successfulSources.join(', '),
      articles: filteredArticles,
      timestamp: new Date().toISOString(),
      count: filteredArticles.length
    };

    res.status(200).json(responseData);

  } catch (error) {
    console.error('❌ Error in Vercel Function:', error);
    
    // في حالة الخطأ، استخدم الأخبار الثابتة
    const fallbackData = getFallbackNews();
    res.status(200).json(fallbackData);
  }
}
